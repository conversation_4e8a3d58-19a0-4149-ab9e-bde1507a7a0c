# ThingsBoard Provisioning Demo

This ESP-IDF project demonstrates device provisioning with ThingsBoard using HTTP POST.

## Setup
1. Replace `WIFI_SSID`, `WIFI_PASS`, `PROV_KEY`, and `PROV_SECRET` in `main/main.c` with your actual WiFi and ThingsBoard credentials.
2. Build and flash the project using ESP-IDF tools:
   ```
   idf.py build
   idf.py -p <PORT> flash monitor
   ```

## Dependencies
- cJSON
- esp_http_client
- esp_wifi
- nvs_flash

## Output
On successful provisioning, the device token will be printed in the serial monitor.
