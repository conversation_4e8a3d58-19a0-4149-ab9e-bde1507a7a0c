ESP-ROM:esp32c3-api1-20210207
Build:Feb  7 2021
rst:0x1 (POWERON),boot:0xc (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fcd5820,len:0x1820
load:0x403cc710,len:0xbcc
load:0x403ce710,len:0x2f5c
entry 0x403cc71a
[0;32mI (30) boot: ESP-IDF GIT-NOTFOUND 2nd stage bootloader[0m
[0;32mI (30) boot: compile time Jul 22 2025 11:31:07[0m
[0;32mI (30) boot: chip revision: v0.3[0m
[0;32mI (34) boot: efuse block revision: v1.2[0m
[0;32mI (38) boot.esp32c3: SPI Speed      : 80MHz[0m
[0;32mI (43) boot.esp32c3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32c3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c0c0020 size=1e514h (124180) map[0m
[0;32mI (124) esp_image: segment 1: paddr=0002e53c vaddr=3fc93800 size=01adch (  6876) load[0m
[0;32mI (126) esp_image: segment 2: paddr=00030020 vaddr=42000020 size=b08ech (723180) map[0m
[0;32mI (247) esp_image: segment 3: paddr=000e0914 vaddr=3fc952dc size=0130ch (  4876) load[0m
[0;32mI (248) esp_image: segment 4: paddr=000e1c28 vaddr=40380000 size=136d4h ( 79572) load[0m
[0;32mI (273) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (273) boot: Disabling RNG early entropy source...[0m
[0;32mI (285) cpu_start: Unicore app[0m
[0;32mI (294) cpu_start: Pro cpu start user code[0m
[0;32mI (294) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (294) app_init: Application information:[0m
[0;32mI (297) app_init: Project name:     thingsboard_provision[0m
[0;32mI (303) app_init: App version:      1[0m
[0;32mI (307) app_init: Compile time:     Jul 22 2025 11:30:43[0m
[0;32mI (313) app_init: ELF file SHA256:  70a721724...[0m
[0;32mI (318) app_init: ESP-IDF:          GIT-NOTFOUND[0m
[0;32mI (324) efuse_init: Min chip rev:     v0.3[0m
[0;32mI (328) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (333) efuse_init: Chip rev:         v0.3[0m
[0;32mI (338) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (345) heap_init: At 3FC9AE90 len 00025170 (148 KiB): RAM[0m
[0;32mI (351) heap_init: At 3FCC0000 len 0001C710 (113 KiB): Retention RAM[0m
[0;32mI (358) heap_init: At 3FCDC710 len 00002950 (10 KiB): Retention RAM[0m
[0;32mI (365) heap_init: At 50000000 len 00001FE8 (7 KiB): RTCRAM[0m
[0;32mI (373) spi_flash: detected chip: generic[0m
[0;32mI (376) spi_flash: flash io: dio[0m
[0;33mW (380) spi_flash: Detected size(4096k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;32mI (394) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (400) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (408) main_task: Started on CPU0[0m
[0;32mI (418) main_task: Calling app_main()[0m
[0;32mI (428) TB_PROVISION: Initializing WiFi[0m
[0;32mI (428) pp: pp rom version: 9387209[0m
[0;32mI (428) net80211: net80211 rom version: 9387209[0m
[0;32mI (438) wifi:wifi driver task: 3fca3b04, prio:23, stack:6656, core=0[0m
[0;32mI (438) wifi:wifi firmware version: b0b320f[0m
[0;32mI (438) wifi:wifi certification version: v7.0[0m
[0;32mI (448) wifi:config NVS flash: enabled[0m
[0;32mI (448) wifi:config nano formating: disabled[0m
[0;32mI (448) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (458) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (458) wifi:Init management short buffer num: 32[0m
[0;32mI (468) wifi:Init dynamic tx buffer num: 32[0m
[0;32mI (468) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (468) wifi:Init static rx buffer size: 1600[0m
[0;32mI (478) wifi:Init static rx buffer num: 10[0m
[0;32mI (478) wifi:Init dynamic rx buffer num: 32[0m
[0;32mI (488) wifi_init: rx ba win: 6[0m
[0;32mI (488) wifi_init: accept mbox: 6[0m
[0;32mI (488) wifi_init: tcpip mbox: 32[0m
[0;32mI (498) wifi_init: udp mbox: 6[0m
[0;32mI (498) wifi_init: tcp mbox: 6[0m
[0;32mI (508) wifi_init: tcp tx win: 5760[0m
[0;32mI (508) wifi_init: tcp rx win: 5760[0m
[0;32mI (508) wifi_init: tcp mss: 1440[0m
[0;32mI (518) wifi_init: WiFi IRAM OP enabled[0m
[0;32mI (518) wifi_init: WiFi RX IRAM OP enabled[0m
[0;33mW (528) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2[0m
[0;32mI (538) phy_init: phy_version 1200,2b7123f9,Feb 18 2025,15:22:21[0m
[0;32mI (578) wifi:mode : sta (58:cf:79:1e:9d:38)[0m
[0;32mI (578) wifi:enable tsf[0m
[0;32mI (578) TB_PROVISION: WiFi started, waiting for connection...[0m
[0;32mI (578) wifi:new:<6,0>, old:<1,0>, ap:<255,255>, sta:<6,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (588) wifi:state: init -> auth (0xb0)[0m
[0;32mI (598) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (608) wifi:state: assoc -> run (0x10)[0m
[0;32mI (638) wifi:connected with kanagaraj, aid = 2, channel 6, BW20, bssid = ba:49:a8:4c:d7:41[0m
[0;32mI (638) wifi:security: WPA2-PSK, phy: bgn, rssi: -45[0m
[0;32mI (648) wifi:pm start, type: 1[0m

[0;32mI (648) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us[0m
[0;32mI (658) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000[0m
[0;32mI (668) wifi:dp: 2, bi: 102400, li: 4, scale listen interval from 307200 us to 409600 us[0m
[0;32mI (668) wifi:AP's beacon interval = 102400 us, DTIM period = 2[0m
[0;32mI (1668) esp_netif_handlers: sta ip: **************, mask: *************, gw: **************[0m
[0;32mI (1668) TB_PROVISION: WiFi connected[0m
[0;32mI (6668) TB_PROVISION: Starting device provisioning[0m
[0;32mI (7828) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (7828) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (7828) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (8838) TB_PROVISION: Starting device provisioning[0m
[0;32mI (9668) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (9668) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (9668) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (10678) TB_PROVISION: Starting device provisioning[0m
[0;32mI (11518) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (11518) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (11518) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (12518) TB_PROVISION: Starting device provisioning[0m
[0;32mI (12778) wifi:<ba-add>idx:0 (ifx:0, ba:49:a8:4c:d7:41), tid:1, ssn:16, winSize:64[0m
[0;32mI (13358) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (13358) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (13358) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (14368) TB_PROVISION: Starting device provisioning[0m
[0;32mI (15428) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (15428) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (15428) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (16438) TB_PROVISION: Starting device provisioning[0m
[0;32mI (17688) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (17688) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (17688) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (18688) TB_PROVISION: Starting device provisioning[0m
[0;32mI (19908) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (19908) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (19908) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (20918) TB_PROVISION: Starting device provisioning[0m
[0;32mI (21978) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (21978) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (21978) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (22988) TB_PROVISION: Starting device provisioning[0m
[0;32mI (23798) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (23798) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (23798) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (24808) TB_PROVISION: Starting device provisioning[0m
[0;32mI (25878) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (25878) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (25878) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (26888) TB_PROVISION: Starting device provisioning[0m
[0;32mI (27718) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (27718) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (27718) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (28718) TB_PROVISION: Starting device provisioning[0m
[0;32mI (29948) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (29948) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (29948) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (30958) TB_PROVISION: Starting device provisioning[0m
[0;32mI (31788) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (31788) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (31788) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (32798) TB_PROVISION: Starting device provisioning[0m
[0;32mI (33838) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (33838) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (33838) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (34848) TB_PROVISION: Starting device provisioning[0m
[0;32mI (35898) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (35898) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (35898) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (36898) TB_PROVISION: Starting device provisioning[0m
[0;32mI (38138) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (38138) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (38138) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (39148) TB_PROVISION: Starting device provisioning[0m
[0;32mI (40218) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (40218) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (40218) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (41228) TB_PROVISION: Starting device provisioning[0m
[0;32mI (42438) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (42438) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (42438) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (43448) TB_PROVISION: Starting device provisioning[0m
[0;32mI (44748) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (44748) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (44748) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (45748) TB_PROVISION: Starting device provisioning[0m
[0;32mI (46948) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (46948) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (46948) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (47958) TB_PROVISION: Starting device provisioning[0m
[0;32mI (49018) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (49018) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (49018) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (50018) TB_PROVISION: Starting device provisioning[0m
[0;32mI (51248) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (51248) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (51248) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (52258) TB_PROVISION: Starting device provisioning[0m
[0;32mI (53288) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (53288) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (53288) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (54298) TB_PROVISION: Starting device provisioning[0m
[0;32mI (55338) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (55338) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (55338) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (56348) TB_PROVISION: Starting device provisioning[0m
[0;32mI (57198) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (57198) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (57198) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (58208) TB_PROVISION: Starting device provisioning[0m
[0;32mI (59028) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (59028) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (59028) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (60038) TB_PROVISION: Starting device provisioning[0m
[0;32mI (60868) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (60868) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (60868) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (61878) TB_PROVISION: Starting device provisioning[0m
[0;32mI (62708) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (62718) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (62718) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (63718) TB_PROVISION: Starting device provisioning[0m
[0;32mI (64788) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (64788) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (64788) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (65798) TB_PROVISION: Starting device provisioning[0m
[0;32mI (67018) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (67018) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (67018) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (68018) TB_PROVISION: Starting device provisioning[0m
[0;32mI (69058) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (69058) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (69068) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (70068) TB_PROVISION: Starting device provisioning[0m
[0;32mI (70718) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (70718) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (70718) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (71728) TB_PROVISION: Starting device provisioning[0m
[0;32mI (72748) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (72748) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (72748) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (73758) TB_PROVISION: Starting device provisioning[0m
[0;32mI (74598) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (74598) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (74598) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (75598) TB_PROVISION: Starting device provisioning[0m
[0;32mI (76238) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (76238) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (76238) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (77238) TB_PROVISION: Starting device provisioning[0m
[0;32mI (78078) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (78078) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (78078) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (79088) TB_PROVISION: Starting device provisioning[0m
[0;32mI (80328) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (80328) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (80328) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (81338) TB_PROVISION: Starting device provisioning[0m
[0;32mI (82198) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (82198) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (82198) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (83208) TB_PROVISION: Starting device provisioning[0m
[0;32mI (84008) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (84018) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (84018) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (85018) TB_PROVISION: Starting device provisioning[0m
[0;32mI (85878) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (85878) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (85878) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (86888) TB_PROVISION: Starting device provisioning[0m
[0;32mI (88128) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (88128) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (88128) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (89138) TB_PROVISION: Starting device provisioning[0m
[0;32mI (90158) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (90158) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (90158) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (91168) TB_PROVISION: Starting device provisioning[0m
[0;32mI (92208) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (92208) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (92208) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (93208) TB_PROVISION: Starting device provisioning[0m
[0;32mI (94268) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (94268) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (94268) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (95278) TB_PROVISION: Starting device provisioning[0m
[0;32mI (95888) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (95888) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (95888) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (96898) TB_PROVISION: Starting device provisioning[0m
[0;32mI (97738) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (97738) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (97738) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (98748) TB_PROVISION: Starting device provisioning[0m
[0;32mI (99988) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (99988) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (99988) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (100998) TB_PROVISION: Starting device provisioning[0m
[0;32mI (102038) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (102038) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (102038) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (103048) TB_PROVISION: Starting device provisioning[0m
[0;32mI (104108) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (104108) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (104108) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (105108) TB_PROVISION: Starting device provisioning[0m
[0;32mI (105938) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (105938) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (105938) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (106948) TB_PROVISION: Starting device provisioning[0m
[0;32mI (107988) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (107988) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (107988) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (108998) TB_PROVISION: Starting device provisioning[0m
[0;32mI (110018) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (110018) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (110028) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (111028) TB_PROVISION: Starting device provisioning[0m
[0;32mI (111888) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (111888) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (111888) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (112888) TB_PROVISION: Starting device provisioning[0m
[0;32mI (113908) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (113908) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (113908) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (114918) TB_PROVISION: Starting device provisioning[0m
[0;32mI (115778) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (115778) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (115778) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (116788) TB_PROVISION: Starting device provisioning[0m
[0;32mI (117818) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (117818) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (117818) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (118828) TB_PROVISION: Starting device provisioning[0m
[0;32mI (119858) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (119858) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (119858) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (120868) TB_PROVISION: Starting device provisioning[0m
[0;32mI (121898) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (121898) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (121898) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (122908) TB_PROVISION: Starting device provisioning[0m
[0;32mI (123958) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (123958) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (123958) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (124968) TB_PROVISION: Starting device provisioning[0m
[0;32mI (125998) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (125998) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (125998) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (127008) TB_PROVISION: Starting device provisioning[0m
[0;32mI (127648) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (127648) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (127648) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (128658) TB_PROVISION: Starting device provisioning[0m
[0;32mI (129908) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (129908) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (129908) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (130918) TB_PROVISION: Starting device provisioning[0m
[0;32mI (132138) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (132138) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (132138) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (133148) TB_PROVISION: Starting device provisioning[0m
[0;32mI (134018) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (134018) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (134018) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (135028) TB_PROVISION: Starting device provisioning[0m
[0;32mI (136238) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (136238) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (136238) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (137248) TB_PROVISION: Starting device provisioning[0m
[0;32mI (138308) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (138308) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (138318) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (139318) TB_PROVISION: Starting device provisioning[0m
[0;32mI (140548) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (140548) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (140548) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (141558) TB_PROVISION: Starting device provisioning[0m
[0;32mI (142588) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (142588) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (142588) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (143588) TB_PROVISION: Starting device provisioning[0m
[0;32mI (144688) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (144688) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (144688) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (145698) TB_PROVISION: Starting device provisioning[0m
[0;32mI (146678) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (146688) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (146688) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (147688) TB_PROVISION: Starting device provisioning[0m
[0;32mI (148958) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (148958) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (148958) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (149968) TB_PROVISION: Starting device provisioning[0m
[0;32mI (150978) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (150978) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (150978) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (151988) TB_PROVISION: Starting device provisioning[0m
[0;32mI (153038) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (153048) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (153048) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (154048) TB_PROVISION: Starting device provisioning[0m
[0;32mI (155288) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (155288) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (155288) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (156298) TB_PROVISION: Starting device provisioning[0m
[0;32mI (157558) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (157558) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (157558) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (158568) TB_PROVISION: Starting device provisioning[0m
[0;32mI (159788) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (159788) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (159788) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (160798) TB_PROVISION: Starting device provisioning[0m
[0;32mI (161838) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (161838) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (161838) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (162848) TB_PROVISION: Starting device provisioning[0m
[0;32mI (163878) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (163888) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (163888) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (164888) TB_PROVISION: Starting device provisioning[0m
[0;32mI (165928) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (165928) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (165928) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (166938) TB_PROVISION: Starting device provisioning[0m
[0;32mI (167578) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (167578) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (167578) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (168588) TB_PROVISION: Starting device provisioning[0m
[0;32mI (169618) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (169618) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (169618) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (170628) TB_PROVISION: Starting device provisioning[0m
[0;32mI (171458) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (171458) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (171458) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (172468) TB_PROVISION: Starting device provisioning[0m
[0;32mI (173538) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (173538) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (173538) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (174538) TB_PROVISION: Starting device provisioning[0m
[0;32mI (175758) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (175758) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (175758) TB_PROVISION: Response content-type: application/json[0m
[0;32mI (176768) TB_PROVISION: Starting device provisioning[0m
[0;32mI (177808) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (177808) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (177808) TB_PROVISION: Response content-type: application/json[0m
