ESP-ROM:esp32c3-api1-20210207
Build:Feb  7 2021
rst:0x1 (POWERON),boot:0xc (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fcd5820,len:0x1820
load:0x403cc710,len:0xbcc
load:0x403ce710,len:0x2f5c
entry 0x403cc71a
[0;32mI (30) boot: ESP-IDF GIT-NOTFOUND 2nd stage bootloader[0m
[0;32mI (30) boot: compile time Jul 22 2025 11:31:07[0m
[0;32mI (30) boot: chip revision: v0.3[0m
[0;32mI (34) boot: efuse block revision: v1.2[0m
[0;32mI (38) boot.esp32c3: SPI Speed      : 80MHz[0m
[0;32mI (43) boot.esp32c3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32c3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c0c0020 size=1e2c0h (123584) map[0m
[0;32mI (124) esp_image: segment 1: paddr=0002e2e8 vaddr=3fc93800 size=01d30h (  7472) load[0m
[0;32mI (126) esp_image: segment 2: paddr=00030020 vaddr=42000020 size=b04fch (722172) map[0m
[0;32mI (247) esp_image: segment 3: paddr=000e0524 vaddr=3fc95530 size=010b8h (  4280) load[0m
[0;32mI (248) esp_image: segment 4: paddr=000e15e4 vaddr=40380000 size=136d4h ( 79572) load[0m
[0;32mI (273) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (273) boot: Disabling RNG early entropy source...[0m
[0;32mI (285) cpu_start: Unicore app[0m
[0;32mI (293) cpu_start: Pro cpu start user code[0m
[0;32mI (293) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (293) app_init: Application information:[0m
[0;32mI (296) app_init: Project name:     thingsboard_provision[0m
[0;32mI (302) app_init: App version:      1[0m
[0;32mI (307) app_init: Compile time:     Jul 22 2025 11:30:43[0m
[0;32mI (313) app_init: ELF file SHA256:  240965185...[0m
[0;32mI (318) app_init: ESP-IDF:          GIT-NOTFOUND[0m
[0;32mI (323) efuse_init: Min chip rev:     v0.3[0m
[0;32mI (328) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (333) efuse_init: Chip rev:         v0.3[0m
[0;32mI (338) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (345) heap_init: At 3FC9AE80 len 00025180 (148 KiB): RAM[0m
[0;32mI (351) heap_init: At 3FCC0000 len 0001C710 (113 KiB): Retention RAM[0m
[0;32mI (358) heap_init: At 3FCDC710 len 00002950 (10 KiB): Retention RAM[0m
[0;32mI (365) heap_init: At 50000000 len 00001FE8 (7 KiB): RTCRAM[0m
[0;32mI (372) spi_flash: detected chip: generic[0m
[0;32mI (376) spi_flash: flash io: dio[0m
[0;33mW (380) spi_flash: Detected size(4096k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;32mI (394) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (400) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (407) main_task: Started on CPU0[0m
[0;32mI (417) main_task: Calling app_main()[0m
[0;32mI (427) TB_PROVISION: Initializing WiFi[0m
[0;32mI (427) pp: pp rom version: 9387209[0m
[0;32mI (427) net80211: net80211 rom version: 9387209[0m
[0;32mI (437) wifi:wifi driver task: 3fca3a78, prio:23, stack:6656, core=0[0m
[0;32mI (437) wifi:wifi firmware version: b0b320f[0m
[0;32mI (437) wifi:wifi certification version: v7.0[0m
[0;32mI (447) wifi:config NVS flash: enabled[0m
[0;32mI (447) wifi:config nano formating: disabled[0m
[0;32mI (447) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (457) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (457) wifi:Init management short buffer num: 32[0m
[0;32mI (467) wifi:Init dynamic tx buffer num: 32[0m
[0;32mI (467) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (467) wifi:Init static rx buffer size: 1600[0m
[0;32mI (477) wifi:Init static rx buffer num: 10[0m
[0;32mI (477) wifi:Init dynamic rx buffer num: 32[0m
[0;32mI (487) wifi_init: rx ba win: 6[0m
[0;32mI (487) wifi_init: accept mbox: 6[0m
[0;32mI (487) wifi_init: tcpip mbox: 32[0m
[0;32mI (497) wifi_init: udp mbox: 6[0m
[0;32mI (497) wifi_init: tcp mbox: 6[0m
[0;32mI (507) wifi_init: tcp tx win: 5760[0m
[0;32mI (507) wifi_init: tcp rx win: 5760[0m
[0;32mI (507) wifi_init: tcp mss: 1440[0m
[0;32mI (517) wifi_init: WiFi IRAM OP enabled[0m
[0;32mI (517) wifi_init: WiFi RX IRAM OP enabled[0m
[0;33mW (527) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2[0m
[0;32mI (557) phy_init: phy_version 1200,2b7123f9,Feb 18 2025,15:22:21[0m
[0;32mI (597) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(0)[0m
[0;32mI (607) wifi:mode : sta (58:cf:79:1e:9d:38)[0m
[0;32mI (607) wifi:enable tsf[0m
[0;32mI (617) TB_PROVISION: WiFi started[0m
[0;32mI (5617) TB_PROVISION: Starting device provisioning[0m
[0;31mE (5617) esp-tls: couldn't get hostname for :thingsboard.cloud: getaddrinfo() returns 202, addrinfo=0x0[0m
[0;31mE (5617) transport_base: Failed to open a new connection: 32769[0m
[0;31mE (5627) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (5627) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (5637) main_task: Returned from app_main()[0m
ESP-ROM:esp32c3-api1-20210207
Build:Feb  7 2021
rst:0x1 (POWERON),boot:0xc (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fcd5820,len:0x1820
load:0x403cc710,len:0xbcc
load:0x403ce710,len:0x2f5c
entry 0x403cc71a
[0;32mI (30) boot: ESP-IDF GIT-NOTFOUND 2nd stage bootloader[0m
[0;32mI (30) boot: compile time Jul 22 2025 11:31:07[0m
[0;32mI (30) boot: chip revision: v0.3[0m
[0;32mI (34) boot: efuse block revision: v1.2[0m
[0;32mI (38) boot.esp32c3: SPI Speed      : 80MHz[0m
[0;32mI (43) boot.esp32c3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32c3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c0c0020 size=1e2c0h (123584) map[0m
[0;32mI (124) esp_image: segment 1: paddr=0002e2e8 vaddr=3fc93800 size=01d30h (  7472) load[0m
[0;32mI (126) esp_image: segment 2: paddr=00030020 vaddr=42000020 size=b04fch (722172) map[0m
[0;32mI (247) esp_image: segment 3: paddr=000e0524 vaddr=3fc95530 size=010b8h (  4280) load[0m
[0;32mI (248) esp_image: segment 4: paddr=000e15e4 vaddr=40380000 size=136d4h ( 79572) load[0m
[0;32mI (273) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (273) boot: Disabling RNG early entropy source...[0m
[0;32mI (285) cpu_start: Unicore app[0m
[0;32mI (293) cpu_start: Pro cpu start user code[0m
[0;32mI (293) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (293) app_init: Application information:[0m
[0;32mI (296) app_init: Project name:     thingsboard_provision[0m
[0;32mI (302) app_init: App version:      1[0m
[0;32mI (307) app_init: Compile time:     Jul 22 2025 11:30:43[0m
[0;32mI (313) app_init: ELF file SHA256:  240965185...[0m
[0;32mI (318) app_init: ESP-IDF:          GIT-NOTFOUND[0m
[0;32mI (323) efuse_init: Min chip rev:     v0.3[0m
[0;32mI (328) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (333) efuse_init: Chip rev:         v0.3[0m
[0;32mI (338) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (345) heap_init: At 3FC9AE80 len 00025180 (148 KiB): RAM[0m
[0;32mI (351) heap_init: At 3FCC0000 len 0001C710 (113 KiB): Retention RAM[0m
[0;32mI (358) heap_init: At 3FCDC710 len 00002950 (10 KiB): Retention RAM[0m
[0;32mI (365) heap_init: At 50000000 len 00001FE8 (7 KiB): RTCRAM[0m
[0;32mI (372) spi_flash: detected chip: generic[0m
[0;32mI (376) spi_flash: flash io: dio[0m
[0;33mW (380) spi_flash: Detected size(4096k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;32mI (394) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (400) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (407) main_task: Started on CPU0[0m
[0;32mI (417) main_task: Calling app_main()[0m
[0;32mI (427) TB_PROVISION: Initializing WiFi[0m
[0;32mI (427) pp: pp rom version: 9387209[0m
[0;32mI (427) net80211: net80211 rom version: 9387209[0m
[0;32mI (437) wifi:wifi driver task: 3fca3afc, prio:23, stack:6656, core=0[0m
[0;32mI (437) wifi:wifi firmware version: b0b320f[0m
[0;32mI (437) wifi:wifi certification version: v7.0[0m
[0;32mI (437) wifi:config NVS flash: enabled[0m
[0;32mI (447) wifi:config nano formating: disabled[0m
[0;32mI (447) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (457) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (457) wifi:Init management short buffer num: 32[0m
[0;32mI (467) wifi:Init dynamic tx buffer num: 32[0m
[0;32mI (467) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (467) wifi:Init static rx buffer size: 1600[0m
[0;32mI (477) wifi:Init static rx buffer num: 10[0m
[0;32mI (477) wifi:Init dynamic rx buffer num: 32[0m
[0;32mI (487) wifi_init: rx ba win: 6[0m
[0;32mI (487) wifi_init: accept mbox: 6[0m
[0;32mI (487) wifi_init: tcpip mbox: 32[0m
[0;32mI (497) wifi_init: udp mbox: 6[0m
[0;32mI (497) wifi_init: tcp mbox: 6[0m
[0;32mI (497) wifi_init: tcp tx win: 5760[0m
[0;32mI (507) wifi_init: tcp rx win: 5760[0m
[0;32mI (507) wifi_init: tcp mss: 1440[0m
[0;32mI (517) wifi_init: WiFi IRAM OP enabled[0m
[0;32mI (517) wifi_init: WiFi RX IRAM OP enabled[0m
[0;33mW (527) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2[0m
[0;32mI (537) phy_init: phy_version 1200,2b7123f9,Feb 18 2025,15:22:21[0m
[0;32mI (577) wifi:mode : sta (58:cf:79:1e:9d:38)[0m
[0;32mI (577) wifi:enable tsf[0m
[0;32mI (577) TB_PROVISION: WiFi started[0m
[0;32mI (5577) TB_PROVISION: Starting device provisioning[0m
[0;31mE (5577) esp-tls: couldn't get hostname for :thingsboard.cloud: getaddrinfo() returns 202, addrinfo=0x0[0m
[0;31mE (5577) transport_base: Failed to open a new connection: 32769[0m
[0;31mE (5587) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (5587) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (5597) main_task: Returned from app_main()[0m
