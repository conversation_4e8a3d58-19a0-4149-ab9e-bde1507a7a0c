ESP-ROM:esp32c3-api1-20210207
Build:Feb  7 2021
rst:0x1 (POWERON),boot:0xc (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fcd5820,len:0x1820
load:0x403cc710,len:0xbcc
load:0x403ce710,len:0x2f5c
entry 0x403cc71a
[0;32mI (30) boot: ESP-IDF GIT-NOTFOUND 2nd stage bootloader[0m
[0;32mI (30) boot: compile time Jul 22 2025 11:31:07[0m
[0;32mI (30) boot: chip revision: v0.3[0m
[0;32mI (34) boot: efuse block revision: v1.2[0m
[0;32mI (38) boot.esp32c3: SPI Speed      : 80MHz[0m
[0;32mI (43) boot.esp32c3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32c3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c0c0020 size=1e484h (124036) map[0m
[0;32mI (124) esp_image: segment 1: paddr=0002e4ac vaddr=3fc93800 size=01b6ch (  7020) load[0m
[0;32mI (126) esp_image: segment 2: paddr=00030020 vaddr=42000020 size=b0868h (723048) map[0m
[0;32mI (247) esp_image: segment 3: paddr=000e0890 vaddr=3fc9536c size=0127ch (  4732) load[0m
[0;32mI (248) esp_image: segment 4: paddr=000e1b14 vaddr=40380000 size=136d4h ( 79572) load[0m
[0;32mI (273) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (273) boot: Disabling RNG early entropy source...[0m
[0;32mI (285) cpu_start: Unicore app[0m
[0;32mI (294) cpu_start: Pro cpu start user code[0m
[0;32mI (294) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (294) app_init: Application information:[0m
[0;32mI (297) app_init: Project name:     thingsboard_provision[0m
[0;32mI (303) app_init: App version:      1[0m
[0;32mI (307) app_init: Compile time:     Jul 22 2025 11:30:43[0m
[0;32mI (313) app_init: ELF file SHA256:  b3bdfb9f0...[0m
[0;32mI (318) app_init: ESP-IDF:          GIT-NOTFOUND[0m
[0;32mI (324) efuse_init: Min chip rev:     v0.3[0m
[0;32mI (328) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (333) efuse_init: Chip rev:         v0.3[0m
[0;32mI (338) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (345) heap_init: At 3FC9AE90 len 00025170 (148 KiB): RAM[0m
[0;32mI (351) heap_init: At 3FCC0000 len 0001C710 (113 KiB): Retention RAM[0m
[0;32mI (358) heap_init: At 3FCDC710 len 00002950 (10 KiB): Retention RAM[0m
[0;32mI (365) heap_init: At 50000000 len 00001FE8 (7 KiB): RTCRAM[0m
[0;32mI (373) spi_flash: detected chip: generic[0m
[0;32mI (376) spi_flash: flash io: dio[0m
[0;33mW (380) spi_flash: Detected size(4096k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;32mI (394) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (400) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (408) main_task: Started on CPU0[0m
[0;32mI (418) main_task: Calling app_main()[0m
[0;32mI (428) TB_PROVISION: Initializing WiFi[0m
[0;32mI (428) pp: pp rom version: 9387209[0m
[0;32mI (428) net80211: net80211 rom version: 9387209[0m
[0;32mI (438) wifi:wifi driver task: 3fca3b88, prio:23, stack:6656, core=0[0m
[0;32mI (438) wifi:wifi firmware version: b0b320f[0m
[0;32mI (438) wifi:wifi certification version: v7.0[0m
[0;32mI (448) wifi:config NVS flash: enabled[0m
[0;32mI (448) wifi:config nano formating: disabled[0m
[0;32mI (448) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (458) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (458) wifi:Init management short buffer num: 32[0m
[0;32mI (468) wifi:Init dynamic tx buffer num: 32[0m
[0;32mI (468) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (468) wifi:Init static rx buffer size: 1600[0m
[0;32mI (478) wifi:Init static rx buffer num: 10[0m
[0;32mI (478) wifi:Init dynamic rx buffer num: 32[0m
[0;32mI (488) wifi_init: rx ba win: 6[0m
[0;32mI (488) wifi_init: accept mbox: 6[0m
[0;32mI (488) wifi_init: tcpip mbox: 32[0m
[0;32mI (498) wifi_init: udp mbox: 6[0m
[0;32mI (498) wifi_init: tcp mbox: 6[0m
[0;32mI (508) wifi_init: tcp tx win: 5760[0m
[0;32mI (508) wifi_init: tcp rx win: 5760[0m
[0;32mI (508) wifi_init: tcp mss: 1440[0m
[0;32mI (518) wifi_init: WiFi IRAM OP enabled[0m
[0;32mI (518) wifi_init: WiFi RX IRAM OP enabled[0m
[0;33mW (528) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2[0m
[0;32mI (538) phy_init: phy_version 1200,2b7123f9,Feb 18 2025,15:22:21[0m
[0;32mI (578) wifi:mode : sta (58:cf:79:1e:9d:38)[0m
[0;32mI (578) wifi:enable tsf[0m
[0;32mI (578) TB_PROVISION: WiFi started, waiting for connection...[0m
[0;32mI (588) wifi:new:<6,0>, old:<1,0>, ap:<255,255>, sta:<6,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (588) wifi:state: init -> auth (0xb0)[0m
[0;32mI (598) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (598) wifi:state: assoc -> run (0x10)[0m
[0;32mI (598) wifi:state: run -> init (0x2c0)[0m
[0;32mI (618) wifi:new:<6,0>, old:<6,0>, ap:<255,255>, sta:<6,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (3058) wifi:new:<6,0>, old:<6,0>, ap:<255,255>, sta:<6,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (3058) wifi:state: init -> auth (0xb0)[0m
[0;32mI (3068) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (3078) wifi:state: assoc -> run (0x10)[0m
[0;32mI (3108) wifi:connected with kanagaraj, aid = 1, channel 6, BW20, bssid = ba:49:a8:4c:d7:41[0m
[0;32mI (3108) wifi:security: WPA2-PSK, phy: bgn, rssi: -33[0m
[0;32mI (3118) wifi:pm start, type: 1[0m

[0;32mI (3118) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us[0m
[0;32mI (3128) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000[0m
[0;32mI (3148) wifi:dp: 2, bi: 102400, li: 4, scale listen interval from 307200 us to 409600 us[0m
[0;32mI (3148) wifi:AP's beacon interval = 102400 us, DTIM period = 2[0m
[0;32mI (4128) esp_netif_handlers: sta ip: **************, mask: *************, gw: **************[0m
[0;32mI (4128) TB_PROVISION: WiFi connected[0m
[0;32mI (9128) TB_PROVISION: Starting device provisioning[0m
[0;32mI (10008) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (11008) TB_PROVISION: Starting device provisioning[0m
[0;32mI (12098) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (13098) TB_PROVISION: Starting device provisioning[0m
[0;32mI (13898) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (14898) TB_PROVISION: Starting device provisioning[0m
[0;32mI (15328) wifi:<ba-add>idx:0 (ifx:0, ba:49:a8:4c:d7:41), tid:1, ssn:16, winSize:64[0m
[0;32mI (15968) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (16978) TB_PROVISION: Starting device provisioning[0m
[0;32mI (18018) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (19018) TB_PROVISION: Starting device provisioning[0m
[0;32mI (20238) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (21248) TB_PROVISION: Starting device provisioning[0m
[0;32mI (22288) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (23298) TB_PROVISION: Starting device provisioning[0m
[0;32mI (23948) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (24948) TB_PROVISION: Starting device provisioning[0m
[0;32mI (26208) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (27208) TB_PROVISION: Starting device provisioning[0m
[0;32mI (28258) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (29258) TB_PROVISION: Starting device provisioning[0m
[0;32mI (30278) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (31278) TB_PROVISION: Starting device provisioning[0m
[0;32mI (32328) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (33328) TB_PROVISION: Starting device provisioning[0m
[0;32mI (33978) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (34978) TB_PROVISION: Starting device provisioning[0m
[0;32mI (35808) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (36808) TB_PROVISION: Starting device provisioning[0m
[0;32mI (37668) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (38668) TB_PROVISION: Starting device provisioning[0m
[0;32mI (39498) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (40498) TB_PROVISION: Starting device provisioning[0m
[0;32mI (41538) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (42548) TB_PROVISION: Starting device provisioning[0m
[0;32mI (43588) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (44588) TB_PROVISION: Starting device provisioning[0m
[0;32mI (45428) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (46438) TB_PROVISION: Starting device provisioning[0m
[0;32mI (47118) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (48118) TB_PROVISION: Starting device provisioning[0m
[0;32mI (49138) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (50138) TB_PROVISION: Starting device provisioning[0m
[0;32mI (51378) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (52378) TB_PROVISION: Starting device provisioning[0m
[0;32mI (53228) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (54228) TB_PROVISION: Starting device provisioning[0m
[0;32mI (55268) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (56268) TB_PROVISION: Starting device provisioning[0m
[0;32mI (57358) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (58358) TB_PROVISION: Starting device provisioning[0m
[0;32mI (59358) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (60358) TB_PROVISION: Starting device provisioning[0m
[0;32mI (61458) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (62458) TB_PROVISION: Starting device provisioning[0m
[0;32mI (63668) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (64668) TB_PROVISION: Starting device provisioning[0m
[0;32mI (65708) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (66708) TB_PROVISION: Starting device provisioning[0m
[0;32mI (67558) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (68558) TB_PROVISION: Starting device provisioning[0m
[0;32mI (69418) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (70428) TB_PROVISION: Starting device provisioning[0m
[0;32mI (73108) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (74108) TB_PROVISION: Starting device provisioning[0m
[0;32mI (75348) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (76348) TB_PROVISION: Starting device provisioning[0m
[0;32mI (77378) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (78388) TB_PROVISION: Starting device provisioning[0m
[0;32mI (79458) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (80458) TB_PROVISION: Starting device provisioning[0m
[0;32mI (81678) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (82688) TB_PROVISION: Starting device provisioning[0m
[0;32mI (83348) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (84348) TB_PROVISION: Starting device provisioning[0m
[0;32mI (85578) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (86578) TB_PROVISION: Starting device provisioning[0m
[0;32mI (87458) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (88458) TB_PROVISION: Starting device provisioning[0m
[0;32mI (89298) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (90298) TB_PROVISION: Starting device provisioning[0m
[0;32mI (91538) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (92538) TB_PROVISION: Starting device provisioning[0m
[0;32mI (93558) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (94558) TB_PROVISION: Starting device provisioning[0m
[0;32mI (95418) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (96418) TB_PROVISION: Starting device provisioning[0m
[0;32mI (97448) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (98458) TB_PROVISION: Starting device provisioning[0m
[0;32mI (99498) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (100498) TB_PROVISION: Starting device provisioning[0m
[0;32mI (101548) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (102548) TB_PROVISION: Starting device provisioning[0m
[0;32mI (103598) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (104598) TB_PROVISION: Starting device provisioning[0m
[0;32mI (105478) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (106478) TB_PROVISION: Starting device provisioning[0m
[0;32mI (107708) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (108708) TB_PROVISION: Starting device provisioning[0m
[0;32mI (109948) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (110948) TB_PROVISION: Starting device provisioning[0m
[0;32mI (111988) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (112998) TB_PROVISION: Starting device provisioning[0m
[0;32mI (114068) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (115068) TB_PROVISION: Starting device provisioning[0m
[0;32mI (116308) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (117308) TB_PROVISION: Starting device provisioning[0m
[0;32mI (118428) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (119428) TB_PROVISION: Starting device provisioning[0m
[0;32mI (120578) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (121578) TB_PROVISION: Starting device provisioning[0m
[0;32mI (122648) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (123648) TB_PROVISION: Starting device provisioning[0m
[0;32mI (124718) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (125718) TB_PROVISION: Starting device provisioning[0m
[0;32mI (126748) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (127748) TB_PROVISION: Starting device provisioning[0m
[0;32mI (128388) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (129388) TB_PROVISION: Starting device provisioning[0m
[0;32mI (130218) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (131228) TB_PROVISION: Starting device provisioning[0m
[0;32mI (132068) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (133068) TB_PROVISION: Starting device provisioning[0m
[0;32mI (134108) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (135108) TB_PROVISION: Starting device provisioning[0m
[0;32mI (135978) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (136978) TB_PROVISION: Starting device provisioning[0m
[0;32mI (138008) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (139008) TB_PROVISION: Starting device provisioning[0m
[0;32mI (140048) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (141048) TB_PROVISION: Starting device provisioning[0m
[0;32mI (142098) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (143098) TB_PROVISION: Starting device provisioning[0m
[0;32mI (144158) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (145168) TB_PROVISION: Starting device provisioning[0m
[0;32mI (145988) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (146988) TB_PROVISION: Starting device provisioning[0m
[0;32mI (147698) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (148698) TB_PROVISION: Starting device provisioning[0m
[0;32mI (149878) TB_PROVISION: Provisioning response length: 65[0m
[0;32mI (150878) TB_PROVISION: Starting device provisioning[0m
