[1/10] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D C:\Kanagaraj_workspace\SE_Projects\sensor\AIOS\thingsboard_provision\build\bootloader\esp-idf\esptool_py && C:\Espressif\python_env\idf5.3_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.3.3/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader/bootloader.bin"

Bootloader binary size 0x53a0 bytes. 0x2c60 bytes (35%) free.


[2/10] No install step for 'bootloader'
[3/10] Completed 'bootloader'
[4/10] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
[5/10] Linking C static library esp-idf\main\libmain.a
[6/10] Generating ld/sections.ld
[7/10] Linking CXX executable thingsboard_provision.elf
[8/10] Generating binary image from built executable
esptool.py v4.8.1

Creating esp32c3 image...

Merged 1 ELF section

Successfully created esp32c3 image.

Generated C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/thingsboard_provision.bin
[9/10] C:\Windows\system32\cmd.exe /C "cd /D C:\Kanagaraj_workspace\SE_Projects\sensor\AIOS\thingsboard_provision\build\esp-idf\esptool_py && C:\Espressif\python_env\idf5.3_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.3.3/components/partition_table/check_sizes.py --offset 0x8000 partition --type app C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/partition_table/partition-table.bin C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/thingsboard_provision.bin"
thingsboard_provision.bin binary size 0xe5210 bytes. Smallest app partition is 0x100000 bytes. 0x1adf0 bytes (10%) free.

[9/10] C:\Windows\system32\cmd.exe /C "cd /D C:\Espressif\frameworks\esp-idf-v5.3.3\components\esptool_py && C:\Espressif\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Espressif/frameworks/esp-idf-v5.3.3 -D SERIAL_TOOL=C:/Espressif/python_env/idf5.3_py3.11_env/Scripts/python.exe;;C:/Espressif/frameworks/esp-idf-v5.3.3/components/esptool_py/esptool/esptool.py;--chip;esp32c3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build -P C:/Espressif/frameworks/esp-idf-v5.3.3/components/esptool_py/run_serial_tool.cmake"
esptool.py --chip esp32c3 -p COM18 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 2MB 0x0 bootloader/bootloader.bin 0x10000 thingsboard_provision.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.8.1
Serial port COM18
Connecting....
Chip is ESP32-C3 (QFN32) (revision v0.3)
Features: WiFi, BLE
Crystal is 40MHz
MAC: 58:cf:79:1e:9d:38
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x000f5fff...
Flash will be erased from 0x00008000 to 0x00008fff...
SHA digest in image updated
Compressed 21408 bytes to 12984...
Writing at 0x00000000... (100 %)
Wrote 21408 bytes (12984 compressed) at 0x00000000 in 0.8 seconds (effective 212.2 kbit/s)...
Hash of data verified.
Compressed 938512 bytes to 538081...
Writing at 0x00010000... (3 %)
Writing at 0x0001c7ab... (6 %)
Writing at 0x000262dd... (9 %)
Writing at 0x00030d15... (12 %)
Writing at 0x00037832... (15 %)
Writing at 0x0003f10c... (18 %)
Writing at 0x00045b52... (21 %)
Writing at 0x0004c77f... (24 %)
Writing at 0x00052d8d... (27 %)
Writing at 0x0005965c... (30 %)
Writing at 0x00060043... (33 %)
Writing at 0x00065c3b... (36 %)
Writing at 0x0006bf29... (39 %)
Writing at 0x00071f56... (42 %)
Writing at 0x000785a1... (45 %)
Writing at 0x0007e6d2... (48 %)
Writing at 0x00084f5f... (51 %)
Writing at 0x0008bae8... (54 %)
Writing at 0x00092d17... (57 %)
Writing at 0x00099cab... (60 %)
Writing at 0x000a0b3a... (63 %)
Writing at 0x000a6ce1... (66 %)
Writing at 0x000ad031... (69 %)
Writing at 0x000b41c0... (72 %)
Writing at 0x000bab89... (75 %)
Writing at 0x000c1210... (78 %)
Writing at 0x000c7a30... (81 %)
Writing at 0x000cd99c... (84 %)
Writing at 0x000d3a64... (87 %)
Writing at 0x000dbb80... (90 %)
Writing at 0x000e289b... (93 %)
Writing at 0x000e8e98... (96 %)
Writing at 0x000effe8... (100 %)
Wrote 938512 bytes (538081 compressed) at 0x00010000 in 15.3 seconds (effective 489.2 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 103...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (103 compressed) at 0x00008000 in 0.1 seconds (effective 271.3 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
