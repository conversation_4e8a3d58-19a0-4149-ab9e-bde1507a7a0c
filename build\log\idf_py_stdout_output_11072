ESP-ROM:esp32c3-api1-20210207
Build:Feb  7 2021
rst:0x1 (POWERON),boot:0xd (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fcd5820,len:0x1820
load:0x403cc710,len:0xbcc
load:0x403ce710,len:0x2f5c
entry 0x403cc71a
[0;32mI (30) boot: ESP-IDF GIT-NOTFOUND 2nd stage bootloader[0m
[0;32mI (30) boot: compile time Jul 22 2025 11:31:07[0m
[0;32mI (30) boot: chip revision: v0.3[0m
[0;32mI (34) boot: efuse block revision: v1.2[0m
[0;32mI (38) boot.esp32c3: SPI Speed      : 80MHz[0m
[0;32mI (43) boot.esp32c3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32c3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c0c0020 size=1e484h (124036) map[0m
[0;32mI (124) esp_image: segment 1: paddr=0002e4ac vaddr=3fc93800 size=01b6ch (  7020) load[0m
[0;32mI (126) esp_image: segment 2: paddr=00030020 vaddr=42000020 size=b0868h (723048) map[0m
[0;32mI (247) esp_image: segment 3: paddr=000e0890 vaddr=3fc9536c size=0127ch (  4732) load[0m
[0;32mI (248) esp_image: segment 4: paddr=000e1b14 vaddr=40380000 size=136d4h ( 79572) load[0m
[0;32mI (273) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (273) boot: Disabling RNG early entropy source...[0m
[0;32mI (285) cpu_start: Unicore app[0m
[0;32mI (294) cpu_start: Pro cpu start user code[0m
[0;32mI (294) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (294) app_init: Application information:[0m
[0;32mI (297) app_init: Project name:     thingsboard_provision[0m
[0;32mI (303) app_init: App version:      1[0m
[0;32mI (307) app_init: Compile time:     Jul 22 2025 11:30:43[0m
[0;32mI (313) app_init: ELF file SHA256:  1b2bdf894...[0m
[0;32mI (318) app_init: ESP-IDF:          GIT-NOTFOUND[0m
[0;32mI (324) efuse_init: Min chip rev:     v0.3[0m
[0;32mI (328) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (333) efuse_init: Chip rev:         v0.3[0m
[0;32mI (338) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (345) heap_init: At 3FC9AE90 len 00025170 (148 KiB): RAM[0m
[0;32mI (351) heap_init: At 3FCC0000 len 0001C710 (113 KiB): Retention RAM[0m
[0;32mI (358) heap_init: At 3FCDC710 len 00002950 (10 KiB): Retention RAM[0m
[0;32mI (365) heap_init: At 50000000 len 00001FE8 (7 KiB): RTCRAM[0m
[0;32mI (373) spi_flash: detected chip: generic[0m
[0;32mI (376) spi_flash: flash io: dio[0m
[0;33mW (380) spi_flash: Detected size(4096k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;32mI (394) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (400) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (408) main_task: Started on CPU0[0m
[0;32mI (418) main_task: Calling app_main()[0m
[0;32mI (428) TB_PROVISION: Initializing WiFi[0m
[0;32mI (428) pp: pp rom version: 9387209[0m
[0;32mI (428) net80211: net80211 rom version: 9387209[0m
[0;32mI (438) wifi:wifi driver task: 3fca3b88, prio:23, stack:6656, core=0[0m
[0;32mI (438) wifi:wifi firmware version: b0b320f[0m
[0;32mI (438) wifi:wifi certification version: v7.0[0m
[0;32mI (448) wifi:config NVS flash: enabled[0m
[0;32mI (448) wifi:config nano formating: disabled[0m
[0;32mI (448) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (458) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (458) wifi:Init management short buffer num: 32[0m
[0;32mI (468) wifi:Init dynamic tx buffer num: 32[0m
[0;32mI (468) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (468) wifi:Init static rx buffer size: 1600[0m
[0;32mI (478) wifi:Init static rx buffer num: 10[0m
[0;32mI (478) wifi:Init dynamic rx buffer num: 32[0m
[0;32mI (488) wifi_init: rx ba win: 6[0m
[0;32mI (488) wifi_init: accept mbox: 6[0m
[0;32mI (488) wifi_init: tcpip mbox: 32[0m
[0;32mI (498) wifi_init: udp mbox: 6[0m
[0;32mI (498) wifi_init: tcp mbox: 6[0m
[0;32mI (508) wifi_init: tcp tx win: 5760[0m
[0;32mI (508) wifi_init: tcp rx win: 5760[0m
[0;32mI (508) wifi_init: tcp mss: 1440[0m
[0;32mI (518) wifi_init: WiFi IRAM OP enabled[0m
[0;32mI (518) wifi_init: WiFi RX IRAM OP enabled[0m
[0;33mW (528) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2[0m
[0;32mI (538) phy_init: phy_version 1200,2b7123f9,Feb 18 2025,15:22:21[0m
[0;32mI (578) wifi:mode : sta (58:cf:79:1e:9d:38)[0m
[0;32mI (578) wifi:enable tsf[0m
[0;32mI (588) TB_PROVISION: WiFi started, waiting for connection...[0m
[0;32mI (648) wifi:new:<6,0>, old:<1,0>, ap:<255,255>, sta:<6,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (648) wifi:state: init -> auth (0xb0)[0m
[0;32mI (658) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (658) wifi:state: assoc -> run (0x10)[0m
[0;32mI (798) wifi:state: run -> init (0x2c0)[0m
[0;32mI (808) wifi:new:<6,0>, old:<6,0>, ap:<255,255>, sta:<6,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (5628) wifi:new:<6,0>, old:<6,0>, ap:<255,255>, sta:<6,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (5628) wifi:state: init -> auth (0xb0)[0m
[0;32mI (5678) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (5678) wifi:state: assoc -> run (0x10)[0m
[0;32mI (5718) wifi:connected with kanagaraj, aid = 1, channel 6, BW20, bssid = ba:49:a8:4c:d7:41[0m
[0;32mI (5718) wifi:security: WPA2-PSK, phy: bgn, rssi: -26[0m
[0;32mI (5718) wifi:pm start, type: 1[0m

[0;32mI (5718) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us[0m
[0;32mI (5728) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000[0m
[0;32mI (5798) wifi:dp: 2, bi: 102400, li: 4, scale listen interval from 307200 us to 409600 us[0m
[0;32mI (5808) wifi:AP's beacon interval = 102400 us, DTIM period = 2[0m
[0;32mI (6738) esp_netif_handlers: sta ip: **************, mask: *************, gw: **************[0m
[0;32mI (6738) TB_PROVISION: WiFi connected[0m
[0;32mI (11738) TB_PROVISION: Starting device provisioning[0m
[0;31mE (12058) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (12058) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (12068) esp-tls: create_ssl_handle failed[0m
[0;31mE (12068) esp-tls: Failed to open new connection[0m
[0;31mE (12078) transport_base: Failed to open a new connection[0m
[0;31mE (12088) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (12088) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (13098) TB_PROVISION: Starting device provisioning[0m
[0;31mE (13558) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (13558) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (13568) esp-tls: create_ssl_handle failed[0m
[0;31mE (13578) esp-tls: Failed to open new connection[0m
[0;31mE (13578) transport_base: Failed to open a new connection[0m
[0;31mE (13588) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (13588) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (14598) TB_PROVISION: Starting device provisioning[0m
[0;31mE (14938) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (14938) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (14948) esp-tls: create_ssl_handle failed[0m
[0;31mE (14948) esp-tls: Failed to open new connection[0m
[0;31mE (14958) transport_base: Failed to open a new connection[0m
[0;31mE (14968) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (14968) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (15978) TB_PROVISION: Starting device provisioning[0m
[0;31mE (16258) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (16258) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (16268) esp-tls: create_ssl_handle failed[0m
[0;31mE (16278) esp-tls: Failed to open new connection[0m
[0;31mE (16278) transport_base: Failed to open a new connection[0m
[0;31mE (16288) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (16288) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (17298) TB_PROVISION: Starting device provisioning[0m
[0;31mE (17598) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (17598) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (17608) esp-tls: create_ssl_handle failed[0m
[0;31mE (17618) esp-tls: Failed to open new connection[0m
[0;31mE (17618) transport_base: Failed to open a new connection[0m
[0;31mE (17628) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (17638) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (18638) TB_PROVISION: Starting device provisioning[0m
[0;31mE (19108) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (19108) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (19118) esp-tls: create_ssl_handle failed[0m
[0;31mE (19128) esp-tls: Failed to open new connection[0m
[0;31mE (19128) transport_base: Failed to open a new connection[0m
[0;31mE (19138) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (19138) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (20148) TB_PROVISION: Starting device provisioning[0m
[0;31mE (20448) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (20448) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (20458) esp-tls: create_ssl_handle failed[0m
[0;31mE (20468) esp-tls: Failed to open new connection[0m
[0;31mE (20468) transport_base: Failed to open a new connection[0m
[0;31mE (20478) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (20488) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (21488) TB_PROVISION: Starting device provisioning[0m
[0;31mE (21948) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (21948) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (21958) esp-tls: create_ssl_handle failed[0m
[0;31mE (21968) esp-tls: Failed to open new connection[0m
[0;31mE (21968) transport_base: Failed to open a new connection[0m
[0;31mE (21978) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (21988) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (22408) wifi:<ba-add>idx:0 (ifx:0, ba:49:a8:4c:d7:41), tid:1, ssn:16, winSize:64[0m
[0;32mI (22988) TB_PROVISION: Starting device provisioning[0m
[0;31mE (23338) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (23338) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (23348) esp-tls: create_ssl_handle failed[0m
[0;31mE (23348) esp-tls: Failed to open new connection[0m
[0;31mE (23358) transport_base: Failed to open a new connection[0m
[0;31mE (23358) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (23368) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (24378) TB_PROVISION: Starting device provisioning[0m
[0;31mE (24958) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (24958) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (24968) esp-tls: create_ssl_handle failed[0m
[0;31mE (24968) esp-tls: Failed to open new connection[0m
[0;31mE (24978) transport_base: Failed to open a new connection[0m
[0;31mE (24988) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (24988) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (25998) TB_PROVISION: Starting device provisioning[0m
[0;31mE (26458) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (26458) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (26468) esp-tls: create_ssl_handle failed[0m
[0;31mE (26478) esp-tls: Failed to open new connection[0m
[0;31mE (26478) transport_base: Failed to open a new connection[0m
[0;31mE (26488) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (26488) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (27498) TB_PROVISION: Starting device provisioning[0m
[0;31mE (27828) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (27828) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (27838) esp-tls: create_ssl_handle failed[0m
[0;31mE (27838) esp-tls: Failed to open new connection[0m
[0;31mE (27848) transport_base: Failed to open a new connection[0m
[0;31mE (27848) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (27858) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (28868) TB_PROVISION: Starting device provisioning[0m
[0;31mE (29288) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (29288) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (29298) esp-tls: create_ssl_handle failed[0m
[0;31mE (29308) esp-tls: Failed to open new connection[0m
[0;31mE (29308) transport_base: Failed to open a new connection[0m
[0;31mE (29318) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (29318) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (30328) TB_PROVISION: Starting device provisioning[0m
[0;31mE (30808) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (30808) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (30818) esp-tls: create_ssl_handle failed[0m
[0;31mE (30818) esp-tls: Failed to open new connection[0m
[0;31mE (30828) transport_base: Failed to open a new connection[0m
[0;31mE (30838) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (30838) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (31848) TB_PROVISION: Starting device provisioning[0m
[0;31mE (32128) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (32128) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (32138) esp-tls: create_ssl_handle failed[0m
[0;31mE (32138) esp-tls: Failed to open new connection[0m
[0;31mE (32148) transport_base: Failed to open a new connection[0m
[0;31mE (32148) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (32158) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (33168) TB_PROVISION: Starting device provisioning[0m
[0;31mE (33558) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (33558) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (33568) esp-tls: create_ssl_handle failed[0m
[0;31mE (33578) esp-tls: Failed to open new connection[0m
[0;31mE (33578) transport_base: Failed to open a new connection[0m
[0;31mE (33588) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (33588) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (34598) TB_PROVISION: Starting device provisioning[0m
[0;31mE (34988) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (34988) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (34998) esp-tls: create_ssl_handle failed[0m
[0;31mE (35008) esp-tls: Failed to open new connection[0m
[0;31mE (35008) transport_base: Failed to open a new connection[0m
[0;31mE (35018) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (35028) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (36028) TB_PROVISION: Starting device provisioning[0m
[0;31mE (36508) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (36508) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (36518) esp-tls: create_ssl_handle failed[0m
[0;31mE (36518) esp-tls: Failed to open new connection[0m
[0;31mE (36528) transport_base: Failed to open a new connection[0m
[0;31mE (36538) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (36538) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (37548) TB_PROVISION: Starting device provisioning[0m
[0;31mE (37858) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (37858) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (37868) esp-tls: create_ssl_handle failed[0m
[0;31mE (37878) esp-tls: Failed to open new connection[0m
[0;31mE (37878) transport_base: Failed to open a new connection[0m
[0;31mE (37888) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (37888) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (38898) TB_PROVISION: Starting device provisioning[0m
[0;31mE (39288) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (39288) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (39308) esp-tls: create_ssl_handle failed[0m
[0;31mE (39308) esp-tls: Failed to open new connection[0m
[0;31mE (39318) transport_base: Failed to open a new connection[0m
[0;31mE (39318) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (39328) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (40328) TB_PROVISION: Starting device provisioning[0m
[0;31mE (40758) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (40758) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (40768) esp-tls: create_ssl_handle failed[0m
[0;31mE (40778) esp-tls: Failed to open new connection[0m
[0;31mE (40778) transport_base: Failed to open a new connection[0m
[0;31mE (40788) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (40788) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (41798) TB_PROVISION: Starting device provisioning[0m
[0;31mE (42208) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (42208) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (42218) esp-tls: create_ssl_handle failed[0m
[0;31mE (42218) esp-tls: Failed to open new connection[0m
[0;31mE (42228) transport_base: Failed to open a new connection[0m
[0;31mE (42228) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (42238) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (43248) TB_PROVISION: Starting device provisioning[0m
[0;31mE (43708) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (43708) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (43718) esp-tls: create_ssl_handle failed[0m
[0;31mE (43728) esp-tls: Failed to open new connection[0m
[0;31mE (43728) transport_base: Failed to open a new connection[0m
[0;31mE (43738) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (43738) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (44748) TB_PROVISION: Starting device provisioning[0m
[0;31mE (45028) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (45028) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (45038) esp-tls: create_ssl_handle failed[0m
[0;31mE (45038) esp-tls: Failed to open new connection[0m
[0;31mE (45048) transport_base: Failed to open a new connection[0m
[0;31mE (45058) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (45058) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (46068) TB_PROVISION: Starting device provisioning[0m
[0;31mE (46528) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (46528) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (46538) esp-tls: create_ssl_handle failed[0m
[0;31mE (46538) esp-tls: Failed to open new connection[0m
[0;31mE (46548) transport_base: Failed to open a new connection[0m
[0;31mE (46558) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (46558) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (47568) TB_PROVISION: Starting device provisioning[0m
[0;31mE (47908) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (47908) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (47918) esp-tls: create_ssl_handle failed[0m
[0;31mE (47918) esp-tls: Failed to open new connection[0m
[0;31mE (47928) transport_base: Failed to open a new connection[0m
[0;31mE (47938) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (47938) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (48948) TB_PROVISION: Starting device provisioning[0m
[0;31mE (49408) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (49408) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (49418) esp-tls: create_ssl_handle failed[0m
[0;31mE (49428) esp-tls: Failed to open new connection[0m
[0;31mE (49428) transport_base: Failed to open a new connection[0m
[0;31mE (49438) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (49438) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (50448) TB_PROVISION: Starting device provisioning[0m
[0;31mE (50928) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (50928) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (50938) esp-tls: create_ssl_handle failed[0m
[0;31mE (50938) esp-tls: Failed to open new connection[0m
[0;31mE (50948) transport_base: Failed to open a new connection[0m
[0;31mE (50958) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (50958) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (51968) TB_PROVISION: Starting device provisioning[0m
[0;31mE (52218) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (52218) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (52228) esp-tls: create_ssl_handle failed[0m
[0;31mE (52228) esp-tls: Failed to open new connection[0m
[0;31mE (52238) transport_base: Failed to open a new connection[0m
[0;31mE (52248) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (52248) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (53258) TB_PROVISION: Starting device provisioning[0m
[0;31mE (53628) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (53628) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (53638) esp-tls: create_ssl_handle failed[0m
[0;31mE (53648) esp-tls: Failed to open new connection[0m
[0;31mE (53648) transport_base: Failed to open a new connection[0m
[0;31mE (53658) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (53658) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (54668) TB_PROVISION: Starting device provisioning[0m
[0;31mE (55098) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (55108) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (55118) esp-tls: create_ssl_handle failed[0m
[0;31mE (55118) esp-tls: Failed to open new connection[0m
[0;31mE (55128) transport_base: Failed to open a new connection[0m
[0;31mE (55128) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (55138) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (56138) TB_PROVISION: Starting device provisioning[0m
[0;31mE (56618) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (56618) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (56628) esp-tls: create_ssl_handle failed[0m
[0;31mE (56628) esp-tls: Failed to open new connection[0m
[0;31mE (56638) transport_base: Failed to open a new connection[0m
[0;31mE (56648) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (56648) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (57658) TB_PROVISION: Starting device provisioning[0m
[0;31mE (57938) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (57948) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (57958) esp-tls: create_ssl_handle failed[0m
[0;31mE (57958) esp-tls: Failed to open new connection[0m
[0;31mE (57968) transport_base: Failed to open a new connection[0m
[0;31mE (57968) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (57978) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (58978) TB_PROVISION: Starting device provisioning[0m
[0;31mE (59458) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (59458) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (59468) esp-tls: create_ssl_handle failed[0m
[0;31mE (59468) esp-tls: Failed to open new connection[0m
[0;31mE (59478) transport_base: Failed to open a new connection[0m
[0;31mE (59488) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (59488) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (60498) TB_PROVISION: Starting device provisioning[0m
[0;31mE (60808) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (60808) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (60818) esp-tls: create_ssl_handle failed[0m
[0;31mE (60828) esp-tls: Failed to open new connection[0m
[0;31mE (60828) transport_base: Failed to open a new connection[0m
[0;31mE (60838) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (60838) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (61848) TB_PROVISION: Starting device provisioning[0m
[0;31mE (62308) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (62308) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (62318) esp-tls: create_ssl_handle failed[0m
[0;31mE (62318) esp-tls: Failed to open new connection[0m
[0;31mE (62328) transport_base: Failed to open a new connection[0m
[0;31mE (62338) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (62338) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (63348) TB_PROVISION: Starting device provisioning[0m
[0;31mE (63688) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (63688) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (63698) esp-tls: create_ssl_handle failed[0m
[0;31mE (63708) esp-tls: Failed to open new connection[0m
[0;31mE (63708) transport_base: Failed to open a new connection[0m
[0;31mE (63718) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (63718) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (64728) TB_PROVISION: Starting device provisioning[0m
[0;31mE (65008) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (65008) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (65018) esp-tls: create_ssl_handle failed[0m
[0;31mE (65018) esp-tls: Failed to open new connection[0m
[0;31mE (65028) transport_base: Failed to open a new connection[0m
[0;31mE (65038) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (65038) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (66048) TB_PROVISION: Starting device provisioning[0m
[0;31mE (66358) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (66358) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (66378) esp-tls: create_ssl_handle failed[0m
[0;31mE (66378) esp-tls: Failed to open new connection[0m
[0;31mE (66388) transport_base: Failed to open a new connection[0m
[0;31mE (66388) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (66398) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (67398) TB_PROVISION: Starting device provisioning[0m
[0;31mE (67768) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (67768) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (67788) esp-tls: create_ssl_handle failed[0m
[0;31mE (67788) esp-tls: Failed to open new connection[0m
[0;31mE (67798) transport_base: Failed to open a new connection[0m
[0;31mE (67798) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (67808) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (68808) TB_PROVISION: Starting device provisioning[0m
[0;31mE (69248) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (69248) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (69258) esp-tls: create_ssl_handle failed[0m
[0;31mE (69258) esp-tls: Failed to open new connection[0m
[0;31mE (69268) transport_base: Failed to open a new connection[0m
[0;31mE (69278) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (69278) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (70288) TB_PROVISION: Starting device provisioning[0m
[0;31mE (70558) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (70558) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (70568) esp-tls: create_ssl_handle failed[0m
[0;31mE (70578) esp-tls: Failed to open new connection[0m
[0;31mE (70578) transport_base: Failed to open a new connection[0m
[0;31mE (70588) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (70588) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (71598) TB_PROVISION: Starting device provisioning[0m
[0;31mE (71878) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (71878) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (71888) esp-tls: create_ssl_handle failed[0m
[0;31mE (71888) esp-tls: Failed to open new connection[0m
[0;31mE (71898) transport_base: Failed to open a new connection[0m
[0;31mE (71898) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (71908) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (72918) TB_PROVISION: Starting device provisioning[0m
[0;31mE (73288) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (73288) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (73298) esp-tls: create_ssl_handle failed[0m
[0;31mE (73308) esp-tls: Failed to open new connection[0m
[0;31mE (73308) transport_base: Failed to open a new connection[0m
[0;31mE (73318) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (73318) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (74328) TB_PROVISION: Starting device provisioning[0m
[0;31mE (74758) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (74758) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (74768) esp-tls: create_ssl_handle failed[0m
[0;31mE (74778) esp-tls: Failed to open new connection[0m
[0;31mE (74778) transport_base: Failed to open a new connection[0m
[0;31mE (74788) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (74798) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (75798) TB_PROVISION: Starting device provisioning[0m
[0;31mE (76258) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (76258) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (76268) esp-tls: create_ssl_handle failed[0m
[0;31mE (76268) esp-tls: Failed to open new connection[0m
[0;31mE (76278) transport_base: Failed to open a new connection[0m
[0;31mE (76278) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (76288) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (77298) TB_PROVISION: Starting device provisioning[0m
[0;31mE (77588) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (77588) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (77598) esp-tls: create_ssl_handle failed[0m
[0;31mE (77608) esp-tls: Failed to open new connection[0m
[0;31mE (77608) transport_base: Failed to open a new connection[0m
[0;31mE (77618) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (77618) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (78628) TB_PROVISION: Starting device provisioning[0m
[0;31mE (78958) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (78958) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (78968) esp-tls: create_ssl_handle failed[0m
[0;31mE (78978) esp-tls: Failed to open new connection[0m
[0;31mE (78978) transport_base: Failed to open a new connection[0m
[0;31mE (78988) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (78988) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (79998) TB_PROVISION: Starting device provisioning[0m
[0;31mE (80268) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (80268) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (80288) esp-tls: create_ssl_handle failed[0m
[0;31mE (80288) esp-tls: Failed to open new connection[0m
[0;31mE (80298) transport_base: Failed to open a new connection[0m
[0;31mE (80298) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (80308) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (81308) TB_PROVISION: Starting device provisioning[0m
[0;31mE (81688) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (81688) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (81698) esp-tls: create_ssl_handle failed[0m
[0;31mE (81698) esp-tls: Failed to open new connection[0m
[0;31mE (81708) transport_base: Failed to open a new connection[0m
[0;31mE (81718) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (81718) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (82728) TB_PROVISION: Starting device provisioning[0m
[0;31mE (83148) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (83148) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (83158) esp-tls: create_ssl_handle failed[0m
[0;31mE (83158) esp-tls: Failed to open new connection[0m
[0;31mE (83168) transport_base: Failed to open a new connection[0m
[0;31mE (83168) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (83178) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (84188) TB_PROVISION: Starting device provisioning[0m
[0;31mE (84658) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (84658) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (84668) esp-tls: create_ssl_handle failed[0m
[0;31mE (84678) esp-tls: Failed to open new connection[0m
[0;31mE (84678) transport_base: Failed to open a new connection[0m
[0;31mE (84688) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (84688) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (85698) TB_PROVISION: Starting device provisioning[0m
[0;31mE (85988) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (85988) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (85998) esp-tls: create_ssl_handle failed[0m
[0;31mE (86008) esp-tls: Failed to open new connection[0m
[0;31mE (86008) transport_base: Failed to open a new connection[0m
[0;31mE (86018) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (86018) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (87028) TB_PROVISION: Starting device provisioning[0m
[0;31mE (87488) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (87488) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (87498) esp-tls: create_ssl_handle failed[0m
[0;31mE (87508) esp-tls: Failed to open new connection[0m
[0;31mE (87508) transport_base: Failed to open a new connection[0m
[0;31mE (87518) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (87518) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (88528) TB_PROVISION: Starting device provisioning[0m
[0;31mE (88848) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (88858) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (88868) esp-tls: create_ssl_handle failed[0m
[0;31mE (88868) esp-tls: Failed to open new connection[0m
[0;31mE (88878) transport_base: Failed to open a new connection[0m
[0;31mE (88878) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (88888) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (89898) TB_PROVISION: Starting device provisioning[0m
[0;31mE (90348) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (90348) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (90368) esp-tls: create_ssl_handle failed[0m
[0;31mE (90368) esp-tls: Failed to open new connection[0m
[0;31mE (90378) transport_base: Failed to open a new connection[0m
[0;31mE (90378) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (90388) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (91388) TB_PROVISION: Starting device provisioning[0m
[0;31mE (91738) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (91738) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (91748) esp-tls: create_ssl_handle failed[0m
[0;31mE (91748) esp-tls: Failed to open new connection[0m
[0;31mE (91758) transport_base: Failed to open a new connection[0m
[0;31mE (91768) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (91768) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (92778) TB_PROVISION: Starting device provisioning[0m
[0;31mE (93058) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (93058) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (93068) esp-tls: create_ssl_handle failed[0m
[0;31mE (93068) esp-tls: Failed to open new connection[0m
[0;31mE (93078) transport_base: Failed to open a new connection[0m
[0;31mE (93088) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (93088) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (94098) TB_PROVISION: Starting device provisioning[0m
[0;31mE (94378) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (94388) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (94398) esp-tls: create_ssl_handle failed[0m
[0;31mE (94398) esp-tls: Failed to open new connection[0m
[0;31mE (94408) transport_base: Failed to open a new connection[0m
[0;31mE (94408) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (94418) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (95418) TB_PROVISION: Starting device provisioning[0m
[0;31mE (95838) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (95838) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (95848) esp-tls: create_ssl_handle failed[0m
[0;31mE (95858) esp-tls: Failed to open new connection[0m
[0;31mE (95858) transport_base: Failed to open a new connection[0m
[0;31mE (95868) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (95868) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (96878) TB_PROVISION: Starting device provisioning[0m
[0;31mE (97248) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (97248) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (97258) esp-tls: create_ssl_handle failed[0m
[0;31mE (97268) esp-tls: Failed to open new connection[0m
[0;31mE (97268) transport_base: Failed to open a new connection[0m
[0;31mE (97278) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (97288) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (98288) TB_PROVISION: Starting device provisioning[0m
[0;31mE (98758) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (98758) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (98768) esp-tls: create_ssl_handle failed[0m
[0;31mE (98778) esp-tls: Failed to open new connection[0m
[0;31mE (98778) transport_base: Failed to open a new connection[0m
[0;31mE (98788) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (98788) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (99798) TB_PROVISION: Starting device provisioning[0m
[0;31mE (100118) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (100118) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (100128) esp-tls: create_ssl_handle failed[0m
[0;31mE (100138) esp-tls: Failed to open new connection[0m
[0;31mE (100138) transport_base: Failed to open a new connection[0m
[0;31mE (100148) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (100148) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (101158) TB_PROVISION: Starting device provisioning[0m
[0;31mE (101458) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (101458) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (101468) esp-tls: create_ssl_handle failed[0m
[0;31mE (101478) esp-tls: Failed to open new connection[0m
[0;31mE (101478) transport_base: Failed to open a new connection[0m
[0;31mE (101488) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (101488) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (102498) TB_PROVISION: Starting device provisioning[0m
[0;31mE (102808) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (102808) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (102818) esp-tls: create_ssl_handle failed[0m
[0;31mE (102818) esp-tls: Failed to open new connection[0m
[0;31mE (102828) transport_base: Failed to open a new connection[0m
[0;31mE (102838) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (102838) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (103848) TB_PROVISION: Starting device provisioning[0m
[0;31mE (104208) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (104218) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (104228) esp-tls: create_ssl_handle failed[0m
[0;31mE (104228) esp-tls: Failed to open new connection[0m
[0;31mE (104238) transport_base: Failed to open a new connection[0m
[0;31mE (104238) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (104248) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (105258) TB_PROVISION: Starting device provisioning[0m
[0;31mE (105648) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (105648) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (105658) esp-tls: create_ssl_handle failed[0m
[0;31mE (105668) esp-tls: Failed to open new connection[0m
[0;31mE (105668) transport_base: Failed to open a new connection[0m
[0;31mE (105678) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (105678) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (106688) TB_PROVISION: Starting device provisioning[0m
[0;31mE (107158) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0;31mE (107158) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0;31mE (107168) esp-tls: create_ssl_handle failed[0m
[0;31mE (107178) esp-tls: Failed to open new connection[0m
[0;31mE (107178) transport_base: Failed to open a new connection[0m
[0;31mE (107188) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (107188) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (108198) TB_PROVISION: Starting device provisioning[0m
