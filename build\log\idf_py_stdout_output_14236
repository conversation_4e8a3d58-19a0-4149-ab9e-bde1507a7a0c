ESP-ROM:esp32c3-api1-20210207
Build:Feb  7 2021
rst:0x1 (POWERON),boot:0xc (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fcd5820,len:0x1820
load:0x403cc710,len:0xbcc
load:0x403ce710,len:0x2f5c
entry 0x403cc71a
[0;32mI (30) boot: ESP-IDF GIT-NOTFOUND 2nd stage bootloader[0m
[0;32mI (30) boot: compile time Jul 22 2025 11:31:07[0m
[0;32mI (30) boot: chip revision: v0.3[0m
[0;32mI (34) boot: efuse block revision: v1.2[0m
[0;32mI (38) boot.esp32c3: SPI Speed      : 80MHz[0m
[0;32mI (43) boot.esp32c3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32c3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c0c0020 size=1e514h (124180) map[0m
[0;32mI (124) esp_image: segment 1: paddr=0002e53c vaddr=3fc93800 size=01adch (  6876) load[0m
[0;32mI (126) esp_image: segment 2: paddr=00030020 vaddr=42000020 size=b08f0h (723184) map[0m
[0;32mI (247) esp_image: segment 3: paddr=000e0918 vaddr=3fc952dc size=0130ch (  4876) load[0m
[0;32mI (248) esp_image: segment 4: paddr=000e1c2c vaddr=40380000 size=136d4h ( 79572) load[0m
[0;32mI (273) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (273) boot: Disabling RNG early entropy source...[0m
[0;32mI (285) cpu_start: Unicore app[0m
[0;32mI (294) cpu_start: Pro cpu start user code[0m
[0;32mI (294) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (294) app_init: Application information:[0m
[0;32mI (297) app_init: Project name:     thingsboard_provision[0m
[0;32mI (303) app_init: App version:      1[0m
[0;32mI (307) app_init: Compile time:     Jul 22 2025 11:30:43[0m
[0;32mI (313) app_init: ELF file SHA256:  b6980f885...[0m
[0;32mI (318) app_init: ESP-IDF:          GIT-NOTFOUND[0m
[0;32mI (324) efuse_init: Min chip rev:     v0.3[0m
[0;32mI (328) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (333) efuse_init: Chip rev:         v0.3[0m
[0;32mI (338) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (345) heap_init: At 3FC9AE90 len 00025170 (148 KiB): RAM[0m
[0;32mI (351) heap_init: At 3FCC0000 len 0001C710 (113 KiB): Retention RAM[0m
[0;32mI (358) heap_init: At 3FCDC710 len 00002950 (10 KiB): Retention RAM[0m
[0;32mI (365) heap_init: At 50000000 len 00001FE8 (7 KiB): RTCRAM[0m
[0;32mI (373) spi_flash: detected chip: generic[0m
[0;32mI (376) spi_flash: flash io: dio[0m
[0;33mW (380) spi_flash: Detected size(4096k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;32mI (394) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (400) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (408) main_task: Started on CPU0[0m
[0;32mI (418) main_task: Calling app_main()[0m
[0;32mI (428) TB_PROVISION: Initializing WiFi[0m
[0;32mI (428) pp: pp rom version: 9387209[0m
[0;32mI (428) net80211: net80211 rom version: 9387209[0m
[0;32mI (438) wifi:wifi driver task: 3fca3b04, prio:23, stack:6656, core=0[0m
[0;32mI (438) wifi:wifi firmware version: b0b320f[0m
[0;32mI (438) wifi:wifi certification version: v7.0[0m
[0;32mI (448) wifi:config NVS flash: enabled[0m
[0;32mI (448) wifi:config nano formating: disabled[0m
[0;32mI (448) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (458) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (458) wifi:Init management short buffer num: 32[0m
[0;32mI (468) wifi:Init dynamic tx buffer num: 32[0m
[0;32mI (468) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (468) wifi:Init static rx buffer size: 1600[0m
[0;32mI (478) wifi:Init static rx buffer num: 10[0m
[0;32mI (478) wifi:Init dynamic rx buffer num: 32[0m
[0;32mI (488) wifi_init: rx ba win: 6[0m
[0;32mI (488) wifi_init: accept mbox: 6[0m
[0;32mI (488) wifi_init: tcpip mbox: 32[0m
[0;32mI (498) wifi_init: udp mbox: 6[0m
[0;32mI (498) wifi_init: tcp mbox: 6[0m
[0;32mI (508) wifi_init: tcp tx win: 5760[0m
[0;32mI (508) wifi_init: tcp rx win: 5760[0m
[0;32mI (508) wifi_init: tcp mss: 1440[0m
[0;32mI (518) wifi_init: WiFi IRAM OP enabled[0m
[0;32mI (518) wifi_init: WiFi RX IRAM OP enabled[0m
[0;33mW (528) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2[0m
[0;32mI (538) phy_init: phy_version 1200,2b7123f9,Feb 18 2025,15:22:21[0m
[0;32mI (578) wifi:mode : sta (58:cf:79:1e:9d:38)[0m
[0;32mI (578) wifi:enable tsf[0m
[0;32mI (578) TB_PROVISION: WiFi started, waiting for connection...[0m
[0;32mI (588) wifi:new:<6,0>, old:<1,0>, ap:<255,255>, sta:<6,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (588) wifi:state: init -> auth (0xb0)[0m
[0;32mI (598) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (608) wifi:state: assoc -> run (0x10)[0m
[0;32mI (628) wifi:connected with kanagaraj, aid = 2, channel 6, BW20, bssid = ba:49:a8:4c:d7:41[0m
[0;32mI (628) wifi:security: WPA2-PSK, phy: bgn, rssi: -35[0m
[0;32mI (628) wifi:pm start, type: 1[0m

[0;32mI (638) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us[0m
[0;32mI (638) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000[0m
[0;32mI (678) wifi:dp: 2, bi: 102400, li: 4, scale listen interval from 307200 us to 409600 us[0m
[0;32mI (678) wifi:AP's beacon interval = 102400 us, DTIM period = 2[0m
[0;32mI (1648) esp_netif_handlers: sta ip: **************, mask: *************, gw: **************[0m
[0;32mI (1648) TB_PROVISION: WiFi connected[0m
[0;32mI (6648) TB_PROVISION: Starting device provisioning[0m
[0;32mI (7668) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (7678) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (7678) TB_PROVISION: Response content-type: 0��?[0m
[0;32mI (8678) TB_PROVISION: Starting device provisioning[0m
[0;32mI (9898) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (9898) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (9898) TB_PROVISION: Response content-type: ���?[0m
[0;32mI (10898) TB_PROVISION: Starting device provisioning[0m
[0;32mI (12148) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (12148) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (12148) TB_PROVISION: Response content-type: H��?[0m
[0;32mI (13158) TB_PROVISION: Starting device provisioning[0m
[0;32mI (13378) wifi:<ba-add>idx:0 (ifx:0, ba:49:a8:4c:d7:41), tid:1, ssn:16, winSize:64[0m
[0;32mI (13998) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (13998) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (13998) TB_PROVISION: Response content-type: ���?[0m
[0;32mI (14998) TB_PROVISION: Starting device provisioning[0m
[0;32mI (15838) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (15838) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (15838) TB_PROVISION: Response content-type: H��?[0m
[0;32mI (16838) TB_PROVISION: Starting device provisioning[0m
[0;32mI (18088) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (18088) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (18088) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (19098) TB_PROVISION: Starting device provisioning[0m
[0;32mI (19728) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (19728) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (19728) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (20728) TB_PROVISION: Starting device provisioning[0m
[0;32mI (21788) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (21788) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (21788) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (22798) TB_PROVISION: Starting device provisioning[0m
[0;32mI (24038) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (24048) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (24048) TB_PROVISION: Response content-type: ���?[0m
[0;32mI (25048) TB_PROVISION: Starting device provisioning[0m
[0;32mI (25868) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (25868) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (25868) TB_PROVISION: Response content-type: ���?[0m
[0;32mI (26878) TB_PROVISION: Starting device provisioning[0m
[0;32mI (27938) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (27938) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (27938) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (28948) TB_PROVISION: Starting device provisioning[0m
[0;32mI (29978) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (29978) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (29978) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (30978) TB_PROVISION: Starting device provisioning[0m
[0;32mI (32218) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (32218) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (32218) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (33228) TB_PROVISION: Starting device provisioning[0m
[0;32mI (34268) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (34268) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (34268) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (35278) TB_PROVISION: Starting device provisioning[0m
[0;32mI (36518) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (36518) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (36518) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (37528) TB_PROVISION: Starting device provisioning[0m
[0;32mI (38568) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (38568) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (38568) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (39578) TB_PROVISION: Starting device provisioning[0m
[0;32mI (40618) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (40618) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (40618) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (41618) TB_PROVISION: Starting device provisioning[0m
[0;32mI (42658) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (42668) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (42668) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (43668) TB_PROVISION: Starting device provisioning[0m
[0;32mI (44328) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (44328) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (44328) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (45338) TB_PROVISION: Starting device provisioning[0m
[0;32mI (46348) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (46348) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (46348) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (47358) TB_PROVISION: Starting device provisioning[0m
[0;32mI (48198) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (48208) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (48208) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (49208) TB_PROVISION: Starting device provisioning[0m
[0;32mI (50038) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (50038) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (50038) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (51038) TB_PROVISION: Starting device provisioning[0m
[0;32mI (51898) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (51898) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (51898) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (52908) TB_PROVISION: Starting device provisioning[0m
[0;32mI (53938) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (53938) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (53938) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (54948) TB_PROVISION: Starting device provisioning[0m
[0;32mI (56208) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (56208) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (56208) TB_PROVISION: Response content-type: p��?[0m
[0;32mI (57218) TB_PROVISION: Starting device provisioning[0m
[0;32mI (58458) TB_PROVISION: HTTP status code: 200[0m
[0;32mI (58458) TB_PROVISION: Provisioning response length: 61[0m
[0;32mI (58458) TB_PROVISION: Response content-type: T��?[0m
[0;32mI (59468) TB_PROVISION: Starting device provisioning[0m
