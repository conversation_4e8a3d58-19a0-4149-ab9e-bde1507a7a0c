#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_http_client.h"
#include "cJSON.h"

#define WIFI_SSID      "kanagaraj"
#define WIFI_PASS      "102030405"
#define PROV_URL       "http://eu.thingsboard.cloud/api/v1/provision"
#define PROV_KEY       "qwtj92oivht7wpvgu192"
#define PROV_SECRET    "wn3klxnn9s5racs4oohw"
#define DEVICE_NAME    "my_devvv"

static const char *TAG = "TB_PROVISION";

static EventGroupHandle_t wifi_event_group;
#define WIFI_CONNECTED_BIT BIT0

static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        esp_wifi_connect();
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        xEventGroupSetBits(wifi_event_group, WIFI_CONNECTED_BIT);
    }
}

static void wifi_init_sta(void)
{
    ESP_LOGI(TAG, "Initializing WiFi");
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();
    wifi_event_group = xEventGroupCreate();
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                       ESP_EVENT_ANY_ID,
                                                       &wifi_event_handler,
                                                       NULL,
                                                       NULL));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                       IP_EVENT_STA_GOT_IP,
                                                       &wifi_event_handler,
                                                       NULL,
                                                       NULL));
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    wifi_config_t wifi_config = {
        .sta = {
            .ssid = WIFI_SSID,
            .password = WIFI_PASS,
        },
    };
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_set_config(ESP_IF_WIFI_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());
    ESP_LOGI(TAG, "WiFi started, waiting for connection...");
    EventBits_t bits = xEventGroupWaitBits(wifi_event_group, WIFI_CONNECTED_BIT, pdFALSE, pdTRUE, portMAX_DELAY);
    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "WiFi connected");
    }
}
char flag = 0;
#define MAX_HTTP_OUTPUT_BUFFER 512
static char output_buffer[MAX_HTTP_OUTPUT_BUFFER + 1];
static int output_len = 0;

static esp_err_t _http_event_handler(esp_http_client_event_t *evt)
{
    switch(evt->event_id) {
        case HTTP_EVENT_ERROR:
            ESP_LOGD(TAG, "HTTP_EVENT_ERROR");
            break;
        case HTTP_EVENT_ON_CONNECTED:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_CONNECTED");
            break;
        case HTTP_EVENT_HEADER_SENT:
            ESP_LOGD(TAG, "HTTP_EVENT_HEADER_SENT");
            break;
        case HTTP_EVENT_ON_HEADER:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_HEADER, key=%s, value=%s", evt->header_key, evt->header_value);
            break;
        case HTTP_EVENT_ON_DATA:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_DATA, len=%d", evt->data_len);
            if (!esp_http_client_is_chunked_response(evt->client)) {
                // Copy the response data to our buffer
                if (output_len + evt->data_len < MAX_HTTP_OUTPUT_BUFFER) {
                    memcpy(output_buffer + output_len, evt->data, evt->data_len);
                    output_len += evt->data_len;
                    output_buffer[output_len] = 0; // Null terminate
                }
            }
            break;
        case HTTP_EVENT_ON_FINISH:
            ESP_LOGD(TAG, "HTTP_EVENT_ON_FINISH");
            break;
        case HTTP_EVENT_DISCONNECTED:
            ESP_LOGD(TAG, "HTTP_EVENT_DISCONNECTED");
            break;
        case HTTP_EVENT_REDIRECT:
            ESP_LOGD(TAG, "HTTP_EVENT_REDIRECT");
            break;
    }
    return ESP_OK;
}

static void provision_device(void)
{
    ESP_LOGI(TAG, "Starting device provisioning");

    // Reset the output buffer for each request
    output_len = 0;
    memset(output_buffer, 0, sizeof(output_buffer));

    char post_data[256];
    snprintf(post_data, sizeof(post_data), "{\"provisionDeviceKey\":\"%s\",\"provisionDeviceSecret\":\"%s\",\"deviceName\":\"%s\"}", PROV_KEY, PROV_SECRET, DEVICE_NAME);

    esp_http_client_config_t config = {
        .url = PROV_URL,
        .event_handler = _http_event_handler,
    };
    esp_http_client_handle_t client = esp_http_client_init(&config);
    esp_http_client_set_method(client, HTTP_METHOD_POST);
    esp_http_client_set_header(client, "Content-Type", "application/json");
    esp_http_client_set_header(client, "User-Agent", "ESP32-ThingsBoard-Client/1.0");
    esp_http_client_set_post_field(client, post_data, strlen(post_data));

    esp_err_t err = esp_http_client_perform(client);
    int status_code = esp_http_client_get_status_code(client);
    ESP_LOGI(TAG, "HTTP status code: %d", status_code);

    if (err == ESP_OK) {
        int content_length = esp_http_client_get_content_length(client);
        ESP_LOGI(TAG, "Provisioning response length: %d", content_length);

        if (output_len > 0) {
            ESP_LOGI(TAG, "Provisioning response: %s", output_buffer);
            cJSON *json = cJSON_Parse(output_buffer);
            if (json) {
                cJSON *credentials = cJSON_GetObjectItem(json, "credentialsValue");
                if (credentials) {
                    ESP_LOGI(TAG, "Provisioned token: %s", credentials->valuestring);
                    flag = 1;
                } else {
                    ESP_LOGW(TAG, "No 'credentialsValue' found in response");
                }
                cJSON_Delete(json);
            } else {
                ESP_LOGW(TAG, "Failed to parse JSON. Raw response: %s", output_buffer);
            }
        } else {
            ESP_LOGW(TAG, "No response body received or empty response.");
        }

        // Log response headers for debugging
        char *header_buf = NULL;
        if (esp_http_client_get_header(client, "content-type", &header_buf) == ESP_OK) {
            ESP_LOGI(TAG, "Response content-type: %s", header_buf);
        }
    } else {
        ESP_LOGE(TAG, "HTTP POST failed: %s", esp_err_to_name(err));
    }
    esp_http_client_cleanup(client);
}

void app_main(void)
{
    ESP_ERROR_CHECK(nvs_flash_init());
    wifi_init_sta();
    vTaskDelay(5000 / portTICK_PERIOD_MS); // Wait for WiFi
    while(!flag){
        provision_device();
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
}
