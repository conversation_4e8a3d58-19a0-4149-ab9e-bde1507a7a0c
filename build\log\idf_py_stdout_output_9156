ESP-ROM:esp32c3-api1-20210207
Build:Feb  7 2021
rst:0x1 (POWERON),boot:0xc (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fcd5820,len:0x1820
load:0x403cc710,len:0xbcc
load:0x403ce710,len:0x2f5c
entry 0x403cc71a
[0;32mI (30) boot: ESP-IDF GIT-NOTFOUND 2nd stage bootloader[0m
[0;32mI (30) boot: compile time Jul 22 2025 11:31:07[0m
[0;32mI (30) boot: chip revision: v0.3[0m
[0;32mI (34) boot: efuse block revision: v1.2[0m
[0;32mI (38) boot.esp32c3: SPI Speed      : 80MHz[0m
[0;32mI (43) boot.esp32c3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32c3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c0c0020 size=1e444h (123972) map[0m
[0;32mI (124) esp_image: segment 1: paddr=0002e46c vaddr=3fc93800 size=01bach (  7084) load[0m
[0;32mI (126) esp_image: segment 2: paddr=00030020 vaddr=42000020 size=b0818h (722968) map[0m
[0;32mI (247) esp_image: segment 3: paddr=000e0840 vaddr=3fc953ac size=0123ch (  4668) load[0m
[0;32mI (248) esp_image: segment 4: paddr=000e1a84 vaddr=40380000 size=136d4h ( 79572) load[0m
[0;32mI (273) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (273) boot: Disabling RNG early entropy source...[0m
[0;32mI (285) cpu_start: Unicore app[0m
[0;32mI (293) cpu_start: Pro cpu start user code[0m
[0;32mI (294) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (294) app_init: Application information:[0m
[0;32mI (296) app_init: Project name:     thingsboard_provision[0m
[0;32mI (303) app_init: App version:      1[0m
[0;32mI (307) app_init: Compile time:     Jul 22 2025 11:30:43[0m
[0;32mI (313) app_init: ELF file SHA256:  be5be0734...[0m
[0;32mI (318) app_init: ESP-IDF:          GIT-NOTFOUND[0m
[0;32mI (323) efuse_init: Min chip rev:     v0.3[0m
[0;32mI (328) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (333) efuse_init: Chip rev:         v0.3[0m
[0;32mI (338) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (345) heap_init: At 3FC9AE80 len 00025180 (148 KiB): RAM[0m
[0;32mI (351) heap_init: At 3FCC0000 len 0001C710 (113 KiB): Retention RAM[0m
[0;32mI (358) heap_init: At 3FCDC710 len 00002950 (10 KiB): Retention RAM[0m
[0;32mI (365) heap_init: At 50000000 len 00001FE8 (7 KiB): RTCRAM[0m
[0;32mI (372) spi_flash: detected chip: generic[0m
[0;32mI (376) spi_flash: flash io: dio[0m
[0;33mW (380) spi_flash: Detected size(4096k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;32mI (394) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (400) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (407) main_task: Started on CPU0[0m
[0;32mI (417) main_task: Calling app_main()[0m
[0;32mI (427) TB_PROVISION: Initializing WiFi[0m
[0;32mI (427) pp: pp rom version: 9387209[0m
[0;32mI (427) net80211: net80211 rom version: 9387209[0m
[0;32mI (437) wifi:wifi driver task: 3fca3b78, prio:23, stack:6656, core=0[0m
[0;32mI (437) wifi:wifi firmware version: b0b320f[0m
[0;32mI (437) wifi:wifi certification version: v7.0[0m
[0;32mI (447) wifi:config NVS flash: enabled[0m
[0;32mI (447) wifi:config nano formating: disabled[0m
[0;32mI (447) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (457) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (457) wifi:Init management short buffer num: 32[0m
[0;32mI (467) wifi:Init dynamic tx buffer num: 32[0m
[0;32mI (467) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (467) wifi:Init static rx buffer size: 1600[0m
[0;32mI (477) wifi:Init static rx buffer num: 10[0m
[0;32mI (477) wifi:Init dynamic rx buffer num: 32[0m
[0;32mI (487) wifi_init: rx ba win: 6[0m
[0;32mI (487) wifi_init: accept mbox: 6[0m
[0;32mI (487) wifi_init: tcpip mbox: 32[0m
[0;32mI (497) wifi_init: udp mbox: 6[0m
[0;32mI (497) wifi_init: tcp mbox: 6[0m
[0;32mI (497) wifi_init: tcp tx win: 5760[0m
[0;32mI (507) wifi_init: tcp rx win: 5760[0m
[0;32mI (507) wifi_init: tcp mss: 1440[0m
[0;32mI (517) wifi_init: WiFi IRAM OP enabled[0m
[0;32mI (517) wifi_init: WiFi RX IRAM OP enabled[0m
[0;33mW (527) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2[0m
[0;32mI (537) phy_init: phy_version 1200,2b7123f9,Feb 18 2025,15:22:21[0m
[0;32mI (577) wifi:mode : sta (58:cf:79:1e:9d:38)[0m
[0;32mI (577) wifi:enable tsf[0m
[0;32mI (577) TB_PROVISION: WiFi started, waiting for connection...[0m
[0;32mI (637) wifi:new:<9,0>, old:<1,0>, ap:<255,255>, sta:<9,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (637) wifi:state: init -> auth (0xb0)[0m
[0;32mI (647) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (647) wifi:state: assoc -> run (0x10)[0m
[0;32mI (1007) wifi:state: run -> init (0x2c0)[0m
[0;32mI (1017) wifi:new:<9,0>, old:<9,0>, ap:<255,255>, sta:<9,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (3447) wifi:new:<9,0>, old:<9,0>, ap:<255,255>, sta:<9,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (3447) wifi:state: init -> auth (0xb0)[0m
[0;32mI (3457) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (3467) wifi:state: assoc -> run (0x10)[0m
[0;32mI (3497) wifi:connected with kanagaraj, aid = 2, channel 9, BW20, bssid = ba:49:a8:4c:d7:41[0m
[0;32mI (3497) wifi:security: WPA2-PSK, phy: bgn, rssi: -32[0m
[0;32mI (3507) wifi:pm start, type: 1[0m

[0;32mI (3507) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us[0m
[0;32mI (3517) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000[0m
[0;32mI (6027) esp_netif_handlers: sta ip: **************, mask: *************, gw: **************[0m
[0;32mI (6027) TB_PROVISION: WiFi connected[0m
[0;32mI (11027) TB_PROVISION: Starting device provisioning[0m
[0;31mE (18027) esp-tls: couldn't get hostname for :thingsboard.cloud: getaddrinfo() returns 202, addrinfo=0x0[0m
[0;31mE (18027) transport_base: Failed to open a new connection: 32769[0m
[0;31mE (18027) HTTP_CLIENT: Connection failed, sock < 0[0m
[0;31mE (18037) TB_PROVISION: HTTP POST failed: ESP_ERR_HTTP_CONNECT[0m
[0;32mI (18037) main_task: Returned from app_main()[0m
[0;32mI (21037) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (30647) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (39657) wifi:bcn_timeout,ap_probe_send_start[0m
ESP-ROM:esp32c3-api1-20210207
Build:Feb  7 2021
rst:0x1 (POWERON),boot:0xc (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fcd5820,len:0x1820
load:0x403cc710,len:0xbcc
load:0x403ce710,len:0x2f5c
entry 0x403cc71a
[0;32mI (30) boot: ESP-IDF GIT-NOTFOUND 2nd stage bootloader[0m
[0;32mI (30) boot: compile time Jul 22 2025 11:31:07[0m
[0;32mI (30) boot: chip revision: v0.3[0m
[0;32mI (34) boot: efuse block revision: v1.2[0m
[0;32mI (38) boot.esp32c3: SPI Speed      : 80MHz[0m
[0;32mI (43) boot.esp32c3: SPI Mode       : DIO[0m
[0;32mI (48) boot.esp32c3: SPI Flash Size : 2MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (58) boot: Partition Table:[0m
[0;32mI (61) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (69) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (76) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (84) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c0c0020 size=1e444h (123972) map[0m
[0;32mI (124) esp_image: segment 1: paddr=0002e46c vaddr=3fc93800 size=01bach (  7084) load[0m
[0;32mI (126) esp_image: segment 2: paddr=00030020 vaddr=42000020 size=b0818h (722968) map[0m
[0;32mI (247) esp_image: segment 3: paddr=000e0840 vaddr=3fc953ac size=0123ch (  4668) load[0m
[0;32mI (248) esp_image: segment 4: paddr=000e1a84 vaddr=40380000 size=136d4h ( 79572) load[0m
[0;32mI (273) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (273) boot: Disabling RNG early entropy source...[0m
[0;32mI (285) cpu_start: Unicore app[0m
[0;32mI (293) cpu_start: Pro cpu start user code[0m
[0;32mI (294) cpu_start: cpu freq: 160000000 Hz[0m
[0;32mI (294) app_init: Application information:[0m
[0;32mI (296) app_init: Project name:     thingsboard_provision[0m
[0;32mI (303) app_init: App version:      1[0m
[0;32mI (307) app_init: Compile time:     Jul 22 2025 11:30:43[0m
[0;32mI (313) app_init: ELF file SHA256:  be5be0734...[0m
[0;32mI (318) app_init: ESP-IDF:          GIT-NOTFOUND[0m
[0;32mI (323) efuse_init: Min chip rev:     v0.3[0m
[0;32mI (328) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (333) efuse_init: Chip rev:         v0.3[0m
[0;32mI (338) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (345) heap_init: At 3FC9AE80 len 00025180 (148 KiB): RAM[0m
[0;32mI (351) heap_init: At 3FCC0000 len 0001C710 (113 KiB): Retention RAM[0m
[0;32mI (358) heap_init: At 3FCDC710 len 00002950 (10 KiB): Retention RAM[0m
[0;32mI (365) heap_init: At 50000000 len 00001FE8 (7 KiB): RTCRAM[0m
[0;32mI (372) spi_flash: detected chip: generic[0m
[0;32mI (376) spi_flash: flash io: dio[0m
[0;33mW (380) spi_flash: Detected size(4096k) larger than the size in the binary image header(2048k). Using the size in the binary image header.[0m
[0;32mI (394) sleep: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (400) sleep: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (407) main_task: Started on CPU0[0m
[0;32mI (417) main_task: Calling app_main()[0m
[0;32mI (427) TB_PROVISION: Initializing WiFi[0m
[0;32mI (427) pp: pp rom version: 9387209[0m
[0;32mI (427) net80211: net80211 rom version: 9387209[0m
[0;32mI (437) wifi:wifi driver task: 3fca3b78, prio:23, stack:6656, core=0[0m
[0;32mI (437) wifi:wifi firmware version: b0b320f[0m
[0;32mI (437) wifi:wifi certification version: v7.0[0m
[0;32mI (447) wifi:config NVS flash: enabled[0m
[0;32mI (447) wifi:config nano formating: disabled[0m
[0;32mI (447) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (457) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (457) wifi:Init management short buffer num: 32[0m
[0;32mI (467) wifi:Init dynamic tx buffer num: 32[0m
[0;32mI (467) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (467) wifi:Init static rx buffer size: 1600[0m
[0;32mI (477) wifi:Init static rx buffer num: 10[0m
[0;32mI (477) wifi:Init dynamic rx buffer num: 32[0m
[0;32mI (487) wifi_init: rx ba win: 6[0m
[0;32mI (487) wifi_init: accept mbox: 6[0m
[0;32mI (487) wifi_init: tcpip mbox: 32[0m
[0;32mI (497) wifi_init: udp mbox: 6[0m
[0;32mI (497) wifi_init: tcp mbox: 6[0m
[0;32mI (497) wifi_init: tcp tx win: 5760[0m
[0;32mI (507) wifi_init: tcp rx win: 5760[0m
[0;32mI (507) wifi_init: tcp mss: 1440[0m
[0;32mI (517) wifi_init: WiFi IRAM OP enabled[0m
[0;32mI (517) wifi_init: WiFi RX IRAM OP enabled[0m
[0;33mW (527) wifi:Password length matches WPA2 standards, authmode threshold changes from OPEN to WPA2[0m
[0;32mI (537) phy_init: phy_version 1200,2b7123f9,Feb 18 2025,15:22:21[0m
[0;32mI (577) wifi:mode : sta (58:cf:79:1e:9d:38)[0m
[0;32mI (577) wifi:enable tsf[0m
[0;32mI (577) TB_PROVISION: WiFi started, waiting for connection...[0m
[0;32mI (577) wifi:new:<9,0>, old:<1,0>, ap:<255,255>, sta:<9,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (587) wifi:state: init -> auth (0xb0)[0m
[0;32mI (597) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (597) wifi:state: assoc -> run (0x10)[0m
[0;32mI (617) wifi:state: run -> init (0x2c0)[0m
[0;32mI (627) wifi:new:<9,0>, old:<9,0>, ap:<255,255>, sta:<9,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (3037) wifi:new:<9,0>, old:<9,0>, ap:<255,255>, sta:<9,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (3037) wifi:state: init -> auth (0xb0)[0m
[0;32mI (3047) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (3057) wifi:state: assoc -> run (0x10)[0m
[0;32mI (3077) wifi:connected with kanagaraj, aid = 2, channel 9, BW20, bssid = ba:49:a8:4c:d7:41[0m
[0;32mI (3077) wifi:security: WPA2-PSK, phy: bgn, rssi: -27[0m
[0;32mI (3077) wifi:pm start, type: 1[0m

[0;32mI (3087) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us[0m
[0;32mI (3087) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000[0m
[0;32mI (4097) esp_netif_handlers: sta ip: **************, mask: *************, gw: **************[0m
[0;32mI (4097) TB_PROVISION: WiFi connected[0m
[0;32mI (9097) TB_PROVISION: Starting device provisioning[0m
[0;32mI (13687) main_task: Returned from app_main()[0m
[0;32mI (21187) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (30197) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (39197) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (48227) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (57247) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (69577) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (78617) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (87617) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (96647) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (105657) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (114677) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (129587) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (138587) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (147627) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (156627) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (165657) wifi:bcn_timeout,ap_probe_send_start[0m
[0;32mI (174687) wifi:bcn_timeout,ap_probe_send_start[0m
