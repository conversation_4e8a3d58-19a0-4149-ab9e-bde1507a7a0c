-- Could NOT find Git (missing: GIT_EXECUTABLE) 
Hint: The project() command has not yet been called.  It sets up system-specific search paths.
-- git rev-parse returned ''
-- ccache will be used for faster recompilation
-- The C compiler identification is GNU 13.2.0
-- The CXX compiler identification is GNU 13.2.0
-- The ASM compiler identification is GNU
-- Found assembler: C:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/riscv32-esp-elf-gcc.exe
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: C:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/riscv32-esp-elf-gcc.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler <PERSON><PERSON> info - done
-- Check for working CXX compiler: C:/Espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/riscv32-esp-elf-g++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned ''
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32c3
-- Project sdkconfig file C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/sdkconfig
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/riscv/ld/rom.api.ld
-- Found Python3: C:/Espressif/python_env/idf5.3_py3.11_env/Scripts/python.exe (found version "3.11.2") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "thingsboard_provision" version: 1
-- Adding linker script C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32c3/ld/esp32c3.rom.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32c3/ld/esp32c3.rom.api.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32c3/ld/esp32c3.rom.bt_funcs.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32c3/ld/esp32c3.rom.libgcc.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32c3/ld/esp32c3.rom.version.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32c3/ld/esp32c3.rom.eco3.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32c3/ld/esp32c3.rom.eco3_bt_funcs.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32c3/ld/esp32c3.rom.newlib.ld
-- Adding linker script C:/Espressif/frameworks/esp-idf-v5.3.3/components/soc/esp32c3/ld/esp32c3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table protobuf-c protocomm pthread riscv sdmmc soc spi_flash spiffs tcp_transport ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant
-- Component paths: C:/Espressif/frameworks/esp-idf-v5.3.3/components/app_trace C:/Espressif/frameworks/esp-idf-v5.3.3/components/app_update C:/Espressif/frameworks/esp-idf-v5.3.3/components/bootloader C:/Espressif/frameworks/esp-idf-v5.3.3/components/bootloader_support C:/Espressif/frameworks/esp-idf-v5.3.3/components/bt C:/Espressif/frameworks/esp-idf-v5.3.3/components/cmock C:/Espressif/frameworks/esp-idf-v5.3.3/components/console C:/Espressif/frameworks/esp-idf-v5.3.3/components/cxx C:/Espressif/frameworks/esp-idf-v5.3.3/components/driver C:/Espressif/frameworks/esp-idf-v5.3.3/components/efuse C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp-tls C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_adc C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_app_format C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_bootloader_format C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_coex C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_common C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ana_cmpr C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_cam C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_dac C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gpio C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gptimer C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_i2c C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_i2s C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_isp C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_jpeg C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ledc C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_mcpwm C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_parlio C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_pcnt C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ppa C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_rmt C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdio C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdm C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdmmc C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdspi C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_spi C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_touch_sens C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_tsens C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_uart C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_usb_serial_jtag C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_eth C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_event C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_gdbstub C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hid C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_client C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_server C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_https_ota C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_https_server C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_lcd C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_local_ctrl C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_mm C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_netif C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_netif_stack C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_partition C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_phy C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_pm C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_psram C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_ringbuf C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_system C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_timer C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_vfs_console C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_wifi C:/Espressif/frameworks/esp-idf-v5.3.3/components/espcoredump C:/Espressif/frameworks/esp-idf-v5.3.3/components/esptool_py C:/Espressif/frameworks/esp-idf-v5.3.3/components/fatfs C:/Espressif/frameworks/esp-idf-v5.3.3/components/freertos C:/Espressif/frameworks/esp-idf-v5.3.3/components/hal C:/Espressif/frameworks/esp-idf-v5.3.3/components/heap C:/Espressif/frameworks/esp-idf-v5.3.3/components/http_parser C:/Espressif/frameworks/esp-idf-v5.3.3/components/idf_test C:/Espressif/frameworks/esp-idf-v5.3.3/components/ieee802154 C:/Espressif/frameworks/esp-idf-v5.3.3/components/json C:/Espressif/frameworks/esp-idf-v5.3.3/components/log C:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/main C:/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls C:/Espressif/frameworks/esp-idf-v5.3.3/components/mqtt C:/Espressif/frameworks/esp-idf-v5.3.3/components/newlib C:/Espressif/frameworks/esp-idf-v5.3.3/components/nvs_flash C:/Espressif/frameworks/esp-idf-v5.3.3/components/nvs_sec_provider C:/Espressif/frameworks/esp-idf-v5.3.3/components/openthread C:/Espressif/frameworks/esp-idf-v5.3.3/components/partition_table C:/Espressif/frameworks/esp-idf-v5.3.3/components/protobuf-c C:/Espressif/frameworks/esp-idf-v5.3.3/components/protocomm C:/Espressif/frameworks/esp-idf-v5.3.3/components/pthread C:/Espressif/frameworks/esp-idf-v5.3.3/components/riscv C:/Espressif/frameworks/esp-idf-v5.3.3/components/sdmmc C:/Espressif/frameworks/esp-idf-v5.3.3/components/soc C:/Espressif/frameworks/esp-idf-v5.3.3/components/spi_flash C:/Espressif/frameworks/esp-idf-v5.3.3/components/spiffs C:/Espressif/frameworks/esp-idf-v5.3.3/components/tcp_transport C:/Espressif/frameworks/esp-idf-v5.3.3/components/ulp C:/Espressif/frameworks/esp-idf-v5.3.3/components/unity C:/Espressif/frameworks/esp-idf-v5.3.3/components/usb C:/Espressif/frameworks/esp-idf-v5.3.3/components/vfs C:/Espressif/frameworks/esp-idf-v5.3.3/components/wear_levelling C:/Espressif/frameworks/esp-idf-v5.3.3/components/wifi_provisioning C:/Espressif/frameworks/esp-idf-v5.3.3/components/wpa_supplicant
-- Configuring done (6.4s)
-- Generating done (1.4s)
-- Build files have been written to: C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build
