[1/10] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D C:\Kanagaraj_workspace\SE_Projects\sensor\AIOS\thingsboard_provision\build\bootloader\esp-idf\esptool_py && C:\Espressif\python_env\idf5.3_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.3.3/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader/bootloader.bin"

Bootloader binary size 0x53a0 bytes. 0x2c60 bytes (35%) free.


[2/10] No install step for 'bootloader'
[3/10] Completed 'bootloader'
[4/10] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
FAILED: esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj 
ccache C:\Espressif\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin\riscv32-esp-elf-gcc.exe -DESP_PLATFORM -DIDF_VER=\"GIT-NOTFOUND\" -DMBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\" -DSOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE -DSOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ -DUNITY_INCLUDE_CONFIG_H -D_GLIBCXX_HAVE_POSIX_SEMAPHORE -D_GLIBCXX_USE_POSIX_SEMAPHORE -D_GNU_SOURCE -D_POSIX_READER_WRITER_LOCKS -IC:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/config -IC:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/main -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/newlib/platform_include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/freertos/config/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/freertos/config/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/freertos/config/riscv/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/freertos/FreeRTOS-Kernel/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/freertos/FreeRTOS-Kernel/portable/riscv/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/freertos/FreeRTOS-Kernel/portable/riscv/include/freertos -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/freertos/esp_additions/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/include/soc -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/include/soc/esp32c3 -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/dma/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/ldo/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/port/esp32c3/. -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hw_support/port/esp32c3/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/heap/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/log/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/soc/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/soc/esp32c3 -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/soc/esp32c3/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/hal/platform_port/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/hal/esp32c3/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/hal/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/include/esp32c3 -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_rom/esp32c3 -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_common/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_system/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_system/port/soc -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_system/port/include/riscv -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_system/port/include/private -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/riscv/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/include/apps -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/include/apps/sntp -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/lwip/src/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/port/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/port/freertos/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/port/esp32xx/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/port/esp32xx/include/arch -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/lwip/port/esp32xx/include/sys -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gpio/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_pm/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls/port/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls/mbedtls/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls/mbedtls/library -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls/esp_crt_bundle/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls/mbedtls/3rdparty/everest/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls/mbedtls/3rdparty/p256-m -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_app_format/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_bootloader_format/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/app_update/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/bootloader_support/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/bootloader_support/bootloader_flash/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_partition/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/efuse/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/efuse/esp32c3/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_mm/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/spi_flash/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/pthread/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_timer/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_gptimer/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_ringbuf/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_uart/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/vfs/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/app_trace/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_event/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/nvs_flash/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_pcnt/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_spi/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_mcpwm/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ana_cmpr/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_i2s/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/sdmmc/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdmmc/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdspi/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdio/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_dac/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_rmt/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_tsens/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_sdm/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_i2c/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ledc/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_parlio/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_usb_serial_jtag/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/driver/deprecated -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/driver/i2c/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/driver/touch_sensor/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/driver/twai/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_phy/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_phy/esp32c3/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_vfs_console/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_netif/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/wpa_supplicant/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/wpa_supplicant/port/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/wpa_supplicant/esp_supplicant/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_coex/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_wifi/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_wifi/include/local -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_wifi/wifi_apps/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_wifi/wifi_apps/nan_app/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_gdbstub/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/unity/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/unity/unity/src -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/cmock/CMock/src -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/console -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/http_parser -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp-tls -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp-tls/esp-tls-crypto -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_adc/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_adc/interface -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_adc/esp32c3/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_adc/deprecated/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_isp/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_cam/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_cam/interface -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_jpeg/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_driver_ppa/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_eth/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_hid/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/tcp_transport/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_client/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_server/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_https_ota/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_https_server/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_psram/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_lcd/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_lcd/interface -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/protobuf-c/protobuf-c -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/protocomm/include/common -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/protocomm/include/security -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/protocomm/include/transports -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/protocomm/include/crypto/srp6a -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/protocomm/proto-c -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_local_ctrl/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/espcoredump/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/espcoredump/include/port/riscv -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/wear_levelling/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/fatfs/diskio -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/fatfs/src -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/fatfs/vfs -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/idf_test/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/idf_test/include/esp32c3 -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/ieee802154/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/json/cJSON -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/mqtt/esp-mqtt/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/nvs_sec_provider/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/spiffs/include -IC:/Espressif/frameworks/esp-idf-v5.3.3/components/wifi_provisioning/include -march=rv32imc_zicsr_zifencei  -fdiagnostics-color=always -ffunction-sections -fdata-sections -Wall -Werror=all -Wno-error=unused-function -Wno-error=unused-variable -Wno-error=unused-but-set-variable -Wno-error=deprecated-declarations -Wextra -Wno-unused-parameter -Wno-sign-compare -Wno-enum-conversion -gdwarf-4 -ggdb -nostartfiles -Og -fno-shrink-wrap -fmacro-prefix-map=C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision=. -fmacro-prefix-map=C:/Espressif/frameworks/esp-idf-v5.3.3=/IDF -fstrict-volatile-bitfields -fno-jump-tables -fno-tree-switch-conversion -std=gnu17 -Wno-old-style-declaration -MD -MT esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj -MF esp-idf\main\CMakeFiles\__idf_main.dir\main.c.obj.d -o esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj -c C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/main/main.c
C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/main/main.c: In function 'provision_device':
C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/main/main.c:119:64: warning: passing argument 3 of 'esp_http_client_get_header' from incompatible pointer type [-Wincompatible-pointer-types]
  119 |         if (esp_http_client_get_header(client, "content-type", header_buf, sizeof(header_buf)) == ESP_OK) {
      |                                                                ^~~~~~~~~~
      |                                                                |
      |                                                                char *
In file included from C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/main/main.c:9:
C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_client/include/esp_http_client.h:344:95: note: expected 'char **' but argument is of type 'char *'
  344 | esp_err_t esp_http_client_get_header(esp_http_client_handle_t client, const char *key, char **value);
      |                                                                                        ~~~~~~~^~~~~
C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/main/main.c:119:13: error: too many arguments to function 'esp_http_client_get_header'
  119 |         if (esp_http_client_get_header(client, "content-type", header_buf, sizeof(header_buf)) == ESP_OK) {
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
C:/Espressif/frameworks/esp-idf-v5.3.3/components/esp_http_client/include/esp_http_client.h:344:11: note: declared here
  344 | esp_err_t esp_http_client_get_header(esp_http_client_handle_t client, const char *key, char **value);
      |           ^~~~~~~~~~~~~~~~~~~~~~~~~~
ninja: build stopped: subcommand failed.
