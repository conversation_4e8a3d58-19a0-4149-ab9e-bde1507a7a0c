[1/10] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D C:\Kanagaraj_workspace\SE_Projects\sensor\AIOS\thingsboard_provision\build\bootloader\esp-idf\esptool_py && C:\Espressif\python_env\idf5.3_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.3.3/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader/bootloader.bin"

Bootloader binary size 0x53a0 bytes. 0x2c60 bytes (35%) free.


[2/10] No install step for 'bootloader'
[3/10] Completed 'bootloader'
[4/10] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/main.c.obj
[5/10] Linking C static library esp-idf\main\libmain.a
[6/10] Generating ld/sections.ld
[7/10] Linking CXX executable thingsboard_provision.elf
[8/10] Generating binary image from built executable
esptool.py v4.8.1

Creating esp32c3 image...

Merged 1 ELF section

Successfully created esp32c3 image.

Generated C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/thingsboard_provision.bin
[9/10] C:\Windows\system32\cmd.exe /C "cd /D C:\Kanagaraj_workspace\SE_Projects\sensor\AIOS\thingsboard_provision\build\esp-idf\esptool_py && C:\Espressif\python_env\idf5.3_py3.11_env\Scripts\python.exe C:/Espressif/frameworks/esp-idf-v5.3.3/components/partition_table/check_sizes.py --offset 0x8000 partition --type app C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/partition_table/partition-table.bin C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/thingsboard_provision.bin"
thingsboard_provision.bin binary size 0xe5160 bytes. Smallest app partition is 0x100000 bytes. 0x1aea0 bytes (11%) free.

[9/10] C:\Windows\system32\cmd.exe /C "cd /D C:\Espressif\frameworks\esp-idf-v5.3.3\components\esptool_py && C:\Espressif\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Espressif/frameworks/esp-idf-v5.3.3 -D SERIAL_TOOL=C:/Espressif/python_env/idf5.3_py3.11_env/Scripts/python.exe;;C:/Espressif/frameworks/esp-idf-v5.3.3/components/esptool_py/esptool/esptool.py;--chip;esp32c3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build -P C:/Espressif/frameworks/esp-idf-v5.3.3/components/esptool_py/run_serial_tool.cmake"
esptool.py --chip esp32c3 -p COM18 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 2MB 0x0 bootloader/bootloader.bin 0x10000 thingsboard_provision.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.8.1
Serial port COM18
Connecting....
Chip is ESP32-C3 (QFN32) (revision v0.3)
Features: WiFi, BLE
Crystal is 40MHz
MAC: 58:cf:79:1e:9d:38
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x000f5fff...
Flash will be erased from 0x00008000 to 0x00008fff...
SHA digest in image updated
Compressed 21408 bytes to 12984...
Writing at 0x00000000... (100 %)
Wrote 21408 bytes (12984 compressed) at 0x00000000 in 0.8 seconds (effective 210.7 kbit/s)...
Hash of data verified.
Compressed 938336 bytes to 537998...
Writing at 0x00010000... (3 %)
Writing at 0x0001c778... (6 %)
Writing at 0x000262c1... (9 %)
Writing at 0x00030d30... (12 %)
Writing at 0x00037858... (15 %)
Writing at 0x0003f0f3... (18 %)
Writing at 0x00045b53... (21 %)
Writing at 0x0004c77b... (24 %)
Writing at 0x00052d94... (27 %)
Writing at 0x0005966e... (30 %)
Writing at 0x00060049... (33 %)
Writing at 0x00065c49... (36 %)
Writing at 0x0006bf35... (39 %)
Writing at 0x00071f45... (42 %)
Writing at 0x000785fa... (45 %)
Writing at 0x0007e6d9... (48 %)
Writing at 0x00084f88... (51 %)
Writing at 0x0008bad6... (54 %)
Writing at 0x00092d2e... (57 %)
Writing at 0x00099ca6... (60 %)
Writing at 0x000a0b3b... (63 %)
Writing at 0x000a6ce8... (66 %)
Writing at 0x000ad033... (69 %)
Writing at 0x000b41db... (72 %)
Writing at 0x000babb1... (75 %)
Writing at 0x000c1215... (78 %)
Writing at 0x000c7a3e... (81 %)
Writing at 0x000cd9ba... (84 %)
Writing at 0x000d3a62... (87 %)
Writing at 0x000dbb79... (90 %)
Writing at 0x000e283c... (93 %)
Writing at 0x000e8e34... (96 %)
Writing at 0x000eff95... (100 %)
Wrote 938336 bytes (537998 compressed) at 0x00010000 in 15.4 seconds (effective 487.5 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 103...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (103 compressed) at 0x00008000 in 0.1 seconds (effective 301.5 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
