{"sources": [{"file": "C:/Kanagaraj_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/CMakeFiles/bootloader"}, {"file": "C:/Kanagar<PERSON>_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/CMakeFiles/bootloader.rule"}, {"file": "C:/Kanagar<PERSON>_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/CMakeFiles/bootloader-complete.rule"}, {"file": "C:/<PERSON>nagar<PERSON>_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "C:/<PERSON>nagar<PERSON>_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "C:/<PERSON>nagar<PERSON>_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "C:/<PERSON>nagar<PERSON>_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "C:/Kanagar<PERSON>_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "C:/<PERSON>nagar<PERSON>_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "C:/<PERSON>nagar<PERSON>_workspace/SE_Projects/sensor/AIOS/thingsboard_provision/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}